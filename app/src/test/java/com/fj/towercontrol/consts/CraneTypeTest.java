package com.fj.towercontrol.consts;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import org.junit.Test;

/**
 * CraneType枚举类测试
 *
 * <AUTHOR>
 */
public class CraneTypeTest {

	@Test
	public void testEnumValues() {
		// Test that all expected enum values exist
		CraneType[] values = CraneType.values();

		assertNotNull("Enum values should not be null", values);
		assertEquals("Should have 4 crane types", 4, values.length);

		// Test specific enum values
		assertEquals("First enum should be YONGMAO", CraneType.YONGMAO, values[0]);
		assertEquals("Second enum should be SANY", CraneType.SANY, values[1]);
		assertEquals("Third enum should be XCMG", CraneType.XCMG, values[2]);
		assertEquals("Fourth enum should be YONGMAO_STT153", CraneType.YONGMAO_STT153, values[3]);
	}

	@Test
	public void testEnumOrdinals() {
		// Test ordinal values
		assertEquals("YONGMAO ordinal should be 0", 0, CraneType.YONGMAO.ordinal());
		assertEquals("SANY ordinal should be 1", 1, CraneType.SANY.ordinal());
		assertEquals("XCMG ordinal should be 2", 2, CraneType.XCMG.ordinal());
		assertEquals("YONGMAO_STT153 ordinal should be 3", 3, CraneType.YONGMAO_STT153.ordinal());
	}

	@Test
	public void testEnumNames() {
		// Test enum names
		assertEquals("YONGMAO name should match", "YONGMAO", CraneType.YONGMAO.name());
		assertEquals("SANY name should match", "SANY", CraneType.SANY.name());
		assertEquals("XCMG name should match", "XCMG", CraneType.XCMG.name());
		assertEquals("YONGMAO_STT153 name should match", "YONGMAO_STT153", CraneType.YONGMAO_STT153.name());
	}

	@Test
	public void testFromOrdinalValidValues() {
		// Test fromOrdinal with valid values
		assertEquals("Ordinal 0 should return YONGMAO", CraneType.YONGMAO, CraneType.fromOrdinal(0));
		assertEquals("Ordinal 1 should return SANY", CraneType.SANY, CraneType.fromOrdinal(1));
		assertEquals("Ordinal 2 should return XCMG", CraneType.XCMG, CraneType.fromOrdinal(2));
		assertEquals("Ordinal 3 should return YONGMAO_STT153", CraneType.YONGMAO_STT153, CraneType.fromOrdinal(3));
	}

	@Test
	public void testFromOrdinalInvalidValues() {
		// Test fromOrdinal with invalid values
		assertNull("Negative ordinal should return null", CraneType.fromOrdinal(-1));
		assertNull("Large ordinal should return null", CraneType.fromOrdinal(100));
		assertNull("Out of range ordinal should return null", CraneType.fromOrdinal(4));
	}

	@Test
	public void testFromOrdinalBoundaryValues() {
		// Test boundary values
		assertNotNull("Minimum valid ordinal should work", CraneType.fromOrdinal(0));
		assertNotNull("Maximum valid ordinal should work", CraneType.fromOrdinal(3));
		assertNull("Just below minimum should return null", CraneType.fromOrdinal(-1));
		assertNull("Just above maximum should return null", CraneType.fromOrdinal(4));
	}

	@Test
	public void testValueOf() {
		// Test valueOf method
		assertEquals("valueOf YONGMAO should work", CraneType.YONGMAO, CraneType.valueOf("YONGMAO"));
		assertEquals("valueOf SANY should work", CraneType.SANY, CraneType.valueOf("SANY"));
		assertEquals("valueOf XCMG should work", CraneType.XCMG, CraneType.valueOf("XCMG"));
		assertEquals("valueOf YONGMAO_STT153 should work", CraneType.YONGMAO_STT153, CraneType.valueOf("YONGMAO_STT153"));
	}

	@Test(expected = IllegalArgumentException.class)
	public void testValueOfInvalidName() {
		// Test valueOf with invalid name should throw exception
		CraneType.valueOf("INVALID_CRANE_TYPE");
	}

	@Test(expected = NullPointerException.class)
	public void testValueOfNullName() {
		// Test valueOf with null should throw exception
		CraneType.valueOf(null);
	}

	@Test
	public void testEnumEquality() {
		// Test enum equality
		assertEquals("Same enum values should be equal", CraneType.YONGMAO, CraneType.YONGMAO);
		assertNotEquals("Different enum values should not be equal", CraneType.YONGMAO, CraneType.SANY);

		// Test with valueOf
		assertEquals("valueOf result should equal direct reference",
			CraneType.YONGMAO, CraneType.valueOf("YONGMAO"));
	}

	@Test
	public void testEnumToString() {
		// Test toString method (should return name by default)
		assertEquals("YONGMAO toString should match name", "YONGMAO", CraneType.YONGMAO.toString());
		assertEquals("SANY toString should match name", "SANY", CraneType.SANY.toString());
		assertEquals("XCMG toString should match name", "XCMG", CraneType.XCMG.toString());
		assertEquals("YONGMAO_STT153 toString should match name", "YONGMAO_STT153", CraneType.YONGMAO_STT153.toString());
	}

	@Test
	public void testEnumHashCode() {
		// Test hashCode consistency
		int hash1 = CraneType.YONGMAO.hashCode();
		int hash2 = CraneType.YONGMAO.hashCode();
		assertEquals("Hash codes should be consistent", hash1, hash2);

		// Different enums should have different hash codes (usually)
		assertNotEquals("Different enums should have different hash codes",
			CraneType.YONGMAO.hashCode(), CraneType.SANY.hashCode());
	}

	@Test
	public void testEnumComparison() {
		// Test enum comparison (based on ordinal)
		assertTrue("YONGMAO should be less than SANY", CraneType.YONGMAO.compareTo(CraneType.SANY) < 0);
		assertTrue("SANY should be greater than YONGMAO", CraneType.SANY.compareTo(CraneType.YONGMAO) > 0);
		assertEquals("Same enum should compare equal", 0, CraneType.YONGMAO.compareTo(CraneType.YONGMAO));

		assertTrue("XCMG should be greater than SANY", CraneType.XCMG.compareTo(CraneType.SANY) > 0);
		assertTrue("YONGMAO_STT153 should be greatest", CraneType.YONGMAO_STT153.compareTo(CraneType.XCMG) > 0);
	}

	@Test
	public void testEnumInSwitch() {
		// Test enum usage in switch statement
		for (CraneType craneType : CraneType.values()) {
			String result = getCraneTypeDescription(craneType);
			assertNotNull("Description should not be null for " + craneType, result);
			assertFalse("Description should not be empty for " + craneType, result.isEmpty());
		}
	}

	@Test
	public void testCraneTypeBusinessLogic() {
		// Test business logic related to crane types

		// Test Yongmao types
		assertTrue("YONGMAO should be a Yongmao type", isYongmaoType(CraneType.YONGMAO));
		assertTrue("YONGMAO_STT153 should be a Yongmao type", isYongmaoType(CraneType.YONGMAO_STT153));
		assertFalse("SANY should not be a Yongmao type", isYongmaoType(CraneType.SANY));
		assertFalse("XCMG should not be a Yongmao type", isYongmaoType(CraneType.XCMG));

		// Test Chinese manufacturers
		assertTrue("SANY should be Chinese", isChineseManufacturer(CraneType.SANY));
		assertTrue("XCMG should be Chinese", isChineseManufacturer(CraneType.XCMG));
		assertTrue("YONGMAO should be Chinese", isChineseManufacturer(CraneType.YONGMAO));
		assertTrue("YONGMAO_STT153 should be Chinese", isChineseManufacturer(CraneType.YONGMAO_STT153));
	}

	@Test
	public void testEnumSerialization() {
		// Test that enum can be serialized/deserialized properly
		for (CraneType craneType : CraneType.values()) {
			// Test ordinal-based serialization
			int ordinal = craneType.ordinal();
			CraneType deserialized = CraneType.fromOrdinal(ordinal);
			assertEquals("Serialization should preserve enum value", craneType, deserialized);

			// Test name-based serialization
			String name = craneType.name();
			CraneType deserializedByName = CraneType.valueOf(name);
			assertEquals("Name-based serialization should preserve enum value", craneType, deserializedByName);
		}
	}

	@Test
	public void testEnumIteration() {
		// Test iterating over all enum values
		int count = 0;
		for (CraneType craneType : CraneType.values()) {
			assertNotNull("Enum value should not be null", craneType);
			count++;
		}
		assertEquals("Should iterate over all 4 values", 4, count);
	}

	@Test
	public void testEnumConstantProperties() {
		// Test that enum constants are properly defined
		assertNotNull("YONGMAO should be defined", CraneType.YONGMAO);
		assertNotNull("SANY should be defined", CraneType.SANY);
		assertNotNull("XCMG should be defined", CraneType.XCMG);
		assertNotNull("YONGMAO_STT153 should be defined", CraneType.YONGMAO_STT153);

		// Test that they are different instances
		assertNotSame("YONGMAO and SANY should be different instances", CraneType.YONGMAO, CraneType.SANY);
		assertNotSame("SANY and XCMG should be different instances", CraneType.SANY, CraneType.XCMG);
		assertNotSame("XCMG and YONGMAO_STT153 should be different instances", CraneType.XCMG, CraneType.YONGMAO_STT153);
	}

	@Test
	public void testFromOrdinalConsistency() {
		// Test that fromOrdinal is consistent with ordinal()
		for (CraneType craneType : CraneType.values()) {
			int ordinal = craneType.ordinal();
			CraneType retrieved = CraneType.fromOrdinal(ordinal);
			assertEquals("fromOrdinal should be consistent with ordinal()", craneType, retrieved);
		}
	}

	@Test
	public void testEnumThreadSafety() {
		// Test that enum operations are thread-safe (basic test)
		// Enums are inherently thread-safe, but we test basic operations

		Runnable task = () -> {
			for (int i = 0; i < 1000; i++) {
				CraneType type = CraneType.fromOrdinal(i % 4);
				assertNotNull("fromOrdinal should work in multithreaded environment", type);

				CraneType[] values = CraneType.values();
				assertEquals("values() should return consistent results", 4, values.length);
			}
		};

		Thread thread1 = new Thread(task);
		Thread thread2 = new Thread(task);

		thread1.start();
		thread2.start();

		try {
			thread1.join();
			thread2.join();
		} catch (InterruptedException e) {
			fail("Thread test was interrupted");
		}
	}

	// Helper methods for testing business logic
	private String getCraneTypeDescription(CraneType craneType) {
		switch (craneType) {
			case YONGMAO:
				return "永茂塔吊";
			case SANY:
				return "三一塔吊";
			case XCMG:
				return "徐工塔吊";
			case YONGMAO_STT153:
				return "永茂平头塔吊STT153";
			default:
				return "未知塔吊类型";
		}
	}

	private boolean isYongmaoType(CraneType craneType) {
		return craneType == CraneType.YONGMAO || craneType == CraneType.YONGMAO_STT153;
	}

	private boolean isChineseManufacturer(CraneType craneType) {
		// All current crane types are Chinese manufacturers
		return true;
	}
}
