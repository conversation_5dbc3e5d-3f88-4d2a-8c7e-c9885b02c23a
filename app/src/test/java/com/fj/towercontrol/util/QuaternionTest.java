package com.fj.towercontrol.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

/**
 * Quaternion四元数类测试
 *
 * <AUTHOR>
 */
public class QuaternionTest {

	@Test
	public void testDefaultConstructor() {
		Quaternion quaternion = new Quaternion();

		assertNotNull("Quaternion should not be null", quaternion);
		assertEquals("Default w should be 0", 0.0f, quaternion.w, 0.000001f);
		assertEquals("Default x should be 0", 0.0f, quaternion.x, 0.000001f);
		assertEquals("Default y should be 0", 0.0f, quaternion.y, 0.000001f);
		assertEquals("Default z should be 0", 0.0f, quaternion.z, 0.000001f);
	}

	@Test
	public void testParameterizedConstructor() {
		float w = 1.0f;
		float x = 0.5f;
		float y = 0.3f;
		float z = 0.2f;

		Quaternion quaternion = new Quaternion(w, x, y, z);

		assertNotNull("Quaternion should not be null", quaternion);
		assertEquals("W should match", w, quaternion.w, 0.000001f);
		assertEquals("X should match", x, quaternion.x, 0.000001f);
		assertEquals("Y should match", y, quaternion.y, 0.000001f);
		assertEquals("Z should match", z, quaternion.z, 0.000001f);
	}

	@Test
	public void testCopyConstructor() {
		Quaternion original = new Quaternion(1.0f, 0.5f, 0.3f, 0.2f);
		Quaternion copy = new Quaternion(original);

		assertNotNull("Copy should not be null", copy);
		assertEquals("W should match original", original.w, copy.w, 0.000001f);
		assertEquals("X should match original", original.x, copy.x, 0.000001f);
		assertEquals("Y should match original", original.y, copy.y, 0.000001f);
		assertEquals("Z should match original", original.z, copy.z, 0.000001f);

		// Verify they are different objects
		assertNotSame("Copy should be different object", original, copy);
	}

	@Test
	public void testIdentityQuaternion() {
		// Identity quaternion: (1, 0, 0, 0)
		Quaternion identity = new Quaternion(1.0f, 0.0f, 0.0f, 0.0f);

		assertEquals("Identity w should be 1", 1.0f, identity.w, 0.000001f);
		assertEquals("Identity x should be 0", 0.0f, identity.x, 0.000001f);
		assertEquals("Identity y should be 0", 0.0f, identity.y, 0.000001f);
		assertEquals("Identity z should be 0", 0.0f, identity.z, 0.000001f);
	}

	@Test
	public void testUnitQuaternion() {
		// Unit quaternion example
		float w = 0.7071f; // cos(45°/2)
		float x = 0.7071f; // sin(45°/2)
		float y = 0.0f;
		float z = 0.0f;

		Quaternion unit = new Quaternion(w, x, y, z);

		// Calculate magnitude: sqrt(w² + x² + y² + z²)
		float magnitude = (float) Math.sqrt(unit.w * unit.w + unit.x * unit.x +
			unit.y * unit.y + unit.z * unit.z);

		assertEquals("Unit quaternion magnitude should be approximately 1",
			1.0f, magnitude, 0.001f);
	}

	@Test
	public void testQuaternionMagnitude() {
		Quaternion q = new Quaternion(2.0f, 3.0f, 4.0f, 5.0f);

		// Calculate magnitude: sqrt(2² + 3² + 4² + 5²) = sqrt(54) ≈ 7.348
		float expectedMagnitude = (float) Math.sqrt(2 * 2 + 3 * 3 + 4 * 4 + 5 * 5);
		float actualMagnitude = (float) Math.sqrt(q.w * q.w + q.x * q.x +
			q.y * q.y + q.z * q.z);

		assertEquals("Quaternion magnitude should be correct",
			expectedMagnitude, actualMagnitude, 0.001f);
	}

	@Test
	public void testQuaternionNormalization() {
		Quaternion q = new Quaternion(2.0f, 3.0f, 4.0f, 5.0f);

		// Calculate magnitude
		float magnitude = (float) Math.sqrt(q.w * q.w + q.x * q.x +
			q.y * q.y + q.z * q.z);

		// Normalize
		Quaternion normalized = new Quaternion(
			q.w / magnitude,
			q.x / magnitude,
			q.y / magnitude,
			q.z / magnitude
		);

		// Check normalized magnitude
		float normalizedMagnitude = (float) Math.sqrt(
			normalized.w * normalized.w + normalized.x * normalized.x +
				normalized.y * normalized.y + normalized.z * normalized.z
		);

		assertEquals("Normalized quaternion magnitude should be 1",
			1.0f, normalizedMagnitude, 0.001f);
	}

	@Test
	public void testQuaternionConjugate() {
		Quaternion q = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);

		// Conjugate: (w, -x, -y, -z)
		Quaternion conjugate = new Quaternion(q.w, -q.x, -q.y, -q.z);

		assertEquals("Conjugate w should be same", q.w, conjugate.w, 0.000001f);
		assertEquals("Conjugate x should be negated", -q.x, conjugate.x, 0.000001f);
		assertEquals("Conjugate y should be negated", -q.y, conjugate.y, 0.000001f);
		assertEquals("Conjugate z should be negated", -q.z, conjugate.z, 0.000001f);
	}

	@Test
	public void testQuaternionAddition() {
		Quaternion q1 = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);
		Quaternion q2 = new Quaternion(0.5f, 1.5f, 2.5f, 3.5f);

		// Addition: (w1+w2, x1+x2, y1+y2, z1+z2)
		Quaternion sum = new Quaternion(
			q1.w + q2.w,
			q1.x + q2.x,
			q1.y + q2.y,
			q1.z + q2.z
		);

		assertEquals("Sum w should be correct", 1.5f, sum.w, 0.000001f);
		assertEquals("Sum x should be correct", 3.5f, sum.x, 0.000001f);
		assertEquals("Sum y should be correct", 5.5f, sum.y, 0.000001f);
		assertEquals("Sum z should be correct", 7.5f, sum.z, 0.000001f);
	}

	@Test
	public void testQuaternionScalarMultiplication() {
		Quaternion q = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);
		float scalar = 2.5f;

		// Scalar multiplication: (s*w, s*x, s*y, s*z)
		Quaternion scaled = new Quaternion(
			q.w * scalar,
			q.x * scalar,
			q.y * scalar,
			q.z * scalar
		);

		assertEquals("Scaled w should be correct", 2.5f, scaled.w, 0.000001f);
		assertEquals("Scaled x should be correct", 5.0f, scaled.x, 0.000001f);
		assertEquals("Scaled y should be correct", 7.5f, scaled.y, 0.000001f);
		assertEquals("Scaled z should be correct", 10.0f, scaled.z, 0.000001f);
	}

	@Test
	public void testQuaternionDotProduct() {
		Quaternion q1 = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);
		Quaternion q2 = new Quaternion(0.5f, 1.5f, 2.5f, 3.5f);

		// Dot product: w1*w2 + x1*x2 + y1*y2 + z1*z2
		float dotProduct = q1.w * q2.w + q1.x * q2.x + q1.y * q2.y + q1.z * q2.z;
		float expectedDot = 1.0f * 0.5f + 2.0f * 1.5f + 3.0f * 2.5f + 4.0f * 3.5f;
		// = 0.5 + 3.0 + 7.5 + 14.0 = 25.0

		assertEquals("Dot product should be correct", expectedDot, dotProduct, 0.000001f);
		assertEquals("Dot product should be 25", 25.0f, dotProduct, 0.000001f);
	}

	@Test
	public void testZeroQuaternion() {
		Quaternion zero = new Quaternion(0.0f, 0.0f, 0.0f, 0.0f);

		assertEquals("Zero w should be 0", 0.0f, zero.w, 0.000001f);
		assertEquals("Zero x should be 0", 0.0f, zero.x, 0.000001f);
		assertEquals("Zero y should be 0", 0.0f, zero.y, 0.000001f);
		assertEquals("Zero z should be 0", 0.0f, zero.z, 0.000001f);

		// Zero quaternion magnitude should be 0
		float magnitude = (float) Math.sqrt(zero.w * zero.w + zero.x * zero.x +
			zero.y * zero.y + zero.z * zero.z);
		assertEquals("Zero quaternion magnitude should be 0", 0.0f, magnitude, 0.000001f);
	}

	@Test
	public void testNegativeValues() {
		Quaternion negative = new Quaternion(-1.0f, -2.0f, -3.0f, -4.0f);

		assertEquals("Negative w should be handled", -1.0f, negative.w, 0.000001f);
		assertEquals("Negative x should be handled", -2.0f, negative.x, 0.000001f);
		assertEquals("Negative y should be handled", -3.0f, negative.y, 0.000001f);
		assertEquals("Negative z should be handled", -4.0f, negative.z, 0.000001f);
	}

	@Test
	public void testLargeValues() {
		float large = 1000000.0f;
		Quaternion largeQ = new Quaternion(large, large, large, large);

		assertEquals("Large w should be handled", large, largeQ.w, 0.1f);
		assertEquals("Large x should be handled", large, largeQ.x, 0.1f);
		assertEquals("Large y should be handled", large, largeQ.y, 0.1f);
		assertEquals("Large z should be handled", large, largeQ.z, 0.1f);
	}

	@Test
	public void testSmallValues() {
		float small = 0.000001f;
		Quaternion smallQ = new Quaternion(small, small, small, small);

		assertEquals("Small w should be handled", small, smallQ.w, 0.0000001f);
		assertEquals("Small x should be handled", small, smallQ.x, 0.0000001f);
		assertEquals("Small y should be handled", small, smallQ.y, 0.0000001f);
		assertEquals("Small z should be handled", small, smallQ.z, 0.0000001f);
	}

	@Test
	public void testQuaternionEquality() {
		Quaternion q1 = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);
		Quaternion q2 = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);
		Quaternion q3 = new Quaternion(1.1f, 2.0f, 3.0f, 4.0f);

		// Test component equality
		assertEquals("Same w values should match", q1.w, q2.w, 0.000001f);
		assertEquals("Same x values should match", q1.x, q2.x, 0.000001f);
		assertEquals("Same y values should match", q1.y, q2.y, 0.000001f);
		assertEquals("Same z values should match", q1.z, q2.z, 0.000001f);

		// Test different values
		assertNotEquals("Different w values should not match", q1.w, q3.w, 0.000001f);
	}

	@Test
	public void testQuaternionImmutabilityAfterCopy() {
		Quaternion original = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);
		Quaternion copy = new Quaternion(original);

		// Modify original
		original.w = 10.0f;
		original.x = 20.0f;
		original.y = 30.0f;
		original.z = 40.0f;

		// Copy should remain unchanged
		assertEquals("Copy w should remain unchanged", 1.0f, copy.w, 0.000001f);
		assertEquals("Copy x should remain unchanged", 2.0f, copy.x, 0.000001f);
		assertEquals("Copy y should remain unchanged", 3.0f, copy.y, 0.000001f);
		assertEquals("Copy z should remain unchanged", 4.0f, copy.z, 0.000001f);
	}

	@Test
	public void testQuaternionToString() {
		Quaternion q = new Quaternion(1.0f, 2.0f, 3.0f, 4.0f);

		String toStringResult = q.toString();

		assertNotNull("toString() should not return null", toStringResult);
		assertTrue("toString() should not be empty", toStringResult.length() > 0);
	}

	@Test
	public void testExtremeFloatValues() {
		// Test with extreme float values
		Quaternion maxQ = new Quaternion(Float.MAX_VALUE, Float.MAX_VALUE,
			Float.MAX_VALUE, Float.MAX_VALUE);
		assertEquals("Max w value should be handled", Float.MAX_VALUE, maxQ.w, 0.0f);
		assertEquals("Max x value should be handled", Float.MAX_VALUE, maxQ.x, 0.0f);
		assertEquals("Max y value should be handled", Float.MAX_VALUE, maxQ.y, 0.0f);
		assertEquals("Max z value should be handled", Float.MAX_VALUE, maxQ.z, 0.0f);

		Quaternion minQ = new Quaternion(Float.MIN_VALUE, Float.MIN_VALUE,
			Float.MIN_VALUE, Float.MIN_VALUE);
		assertEquals("Min w value should be handled", Float.MIN_VALUE, minQ.w, 0.0f);
		assertEquals("Min x value should be handled", Float.MIN_VALUE, minQ.x, 0.0f);
		assertEquals("Min y value should be handled", Float.MIN_VALUE, minQ.y, 0.0f);
		assertEquals("Min z value should be handled", Float.MIN_VALUE, minQ.z, 0.0f);
	}

	@Test
	public void testSpecialFloatValues() {
		// Test with special float values
		Quaternion infQ = new Quaternion(Float.POSITIVE_INFINITY, Float.NEGATIVE_INFINITY,
			Float.POSITIVE_INFINITY, Float.NEGATIVE_INFINITY);
		assertEquals("Positive infinity w should be preserved",
			Float.POSITIVE_INFINITY, infQ.w, 0.0f);
		assertEquals("Negative infinity x should be preserved",
			Float.NEGATIVE_INFINITY, infQ.x, 0.0f);
		assertEquals("Positive infinity y should be preserved",
			Float.POSITIVE_INFINITY, infQ.y, 0.0f);
		assertEquals("Negative infinity z should be preserved",
			Float.NEGATIVE_INFINITY, infQ.z, 0.0f);

		Quaternion nanQ = new Quaternion(Float.NaN, Float.NaN, Float.NaN, Float.NaN);
		assertTrue("NaN w should be preserved", Float.isNaN(nanQ.w));
		assertTrue("NaN x should be preserved", Float.isNaN(nanQ.x));
		assertTrue("NaN y should be preserved", Float.isNaN(nanQ.y));
		assertTrue("NaN z should be preserved", Float.isNaN(nanQ.z));
	}

	@Test
	public void testQuaternionConsistency() {
		// Test that quaternion values remain consistent across operations
		for (int i = 0; i < 100; i++) {
			float w = i * 0.01f;
			float x = i * 0.02f;
			float y = i * 0.03f;
			float z = i * 0.04f;

			Quaternion q = new Quaternion(w, x, y, z);

			assertEquals("W should be consistent at iteration " + i,
				w, q.w, 0.000001f);
			assertEquals("X should be consistent at iteration " + i,
				x, q.x, 0.000001f);
			assertEquals("Y should be consistent at iteration " + i,
				y, q.y, 0.000001f);
			assertEquals("Z should be consistent at iteration " + i,
				z, q.z, 0.000001f);
		}
	}

	@Test
	public void testRealWorldRotationScenario() {
		// Test a real-world rotation scenario for tower crane
		// 90-degree rotation around Z-axis
		float angle = (float) Math.toRadians(90); // 90 degrees in radians
		float halfAngle = angle / 2.0f;

		Quaternion rotation = new Quaternion(
			(float) Math.cos(halfAngle), // w
			0.0f,                        // x
			0.0f,                        // y
			(float) Math.sin(halfAngle)  // z
		);

		// Verify it's approximately a unit quaternion
		float magnitude = (float) Math.sqrt(rotation.w * rotation.w + rotation.x * rotation.x +
			rotation.y * rotation.y + rotation.z * rotation.z);
		assertEquals("Rotation quaternion should be unit", 1.0f, magnitude, 0.001f);

		// Verify components for 90-degree Z rotation
		assertEquals("90-degree Z rotation w component",
			(float) Math.cos(Math.toRadians(45)), rotation.w, 0.001f);
		assertEquals("90-degree Z rotation x component", 0.0f, rotation.x, 0.001f);
		assertEquals("90-degree Z rotation y component", 0.0f, rotation.y, 0.001f);
		assertEquals("90-degree Z rotation z component",
			(float) Math.sin(Math.toRadians(45)), rotation.z, 0.001f);
	}
}
