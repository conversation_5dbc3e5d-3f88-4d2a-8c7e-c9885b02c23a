package com.fj.towercontrol.data.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

/**
 * CargoWeightInfo实体类测试
 *
 * <AUTHOR>
 */
public class CargoWeightInfoTest {

	@Test
	public void testConstructor() {
		int grossWeight = 1500;
		int tareWeight = 200;
		int netWeight = 1300;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(grossWeight, tareWeight, netWeight);

		assertNotNull("CargoWeightInfo should not be null", cargoWeight);
		assertEquals("Gross weight should match", grossWeight, cargoWeight.getGrossWeight());
		assertEquals("Tare weight should match", tareWeight, cargoWeight.getTareWeight());
		assertEquals("Net weight should match", netWeight, cargoWeight.getNetWeight());
	}

	@Test
	public void testConstructorWithZeroValues() {
		int grossWeight = 0;
		int tareWeight = 0;
		int netWeight = 0;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(grossWeight, tareWeight, netWeight);

		assertEquals("Zero gross weight should be handled", 0, cargoWeight.getGrossWeight());
		assertEquals("Zero tare weight should be handled", 0, cargoWeight.getTareWeight());
		assertEquals("Zero net weight should be handled", 0, cargoWeight.getNetWeight());
	}

	@Test
	public void testConstructorWithNegativeValues() {
		int grossWeight = -100;
		int tareWeight = -50;
		int netWeight = -50;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(grossWeight, tareWeight, netWeight);

		assertEquals("Negative gross weight should be stored", -100, cargoWeight.getGrossWeight());
		assertEquals("Negative tare weight should be stored", -50, cargoWeight.getTareWeight());
		assertEquals("Negative net weight should be stored", -50, cargoWeight.getNetWeight());
	}

	@Test
	public void testGrossWeightGetterSetter() {
		CargoWeightInfo cargoWeight = new CargoWeightInfo(1000, 100, 900);

		int newGrossWeight = 2000;
		cargoWeight.setGrossWeight(newGrossWeight);

		assertEquals("Updated gross weight should match", newGrossWeight, cargoWeight.getGrossWeight());
	}

	@Test
	public void testTareWeightGetterSetter() {
		CargoWeightInfo cargoWeight = new CargoWeightInfo(1000, 100, 900);

		int newTareWeight = 150;
		cargoWeight.setTareWeight(newTareWeight);

		assertEquals("Updated tare weight should match", newTareWeight, cargoWeight.getTareWeight());
	}

	@Test
	public void testNetWeightGetterSetter() {
		CargoWeightInfo cargoWeight = new CargoWeightInfo(1000, 100, 900);

		int newNetWeight = 850;
		cargoWeight.setNetWeight(newNetWeight);

		assertEquals("Updated net weight should match", newNetWeight, cargoWeight.getNetWeight());
	}

	@Test
	public void testWeightCalculationLogic() {
		// Test typical weight calculation: Net Weight = Gross Weight - Tare Weight
		int grossWeight = 1500;
		int tareWeight = 200;
		int expectedNetWeight = grossWeight - tareWeight; // 1300

		CargoWeightInfo cargoWeight = new CargoWeightInfo(grossWeight, tareWeight, expectedNetWeight);

		assertEquals("Gross weight should match", grossWeight, cargoWeight.getGrossWeight());
		assertEquals("Tare weight should match", tareWeight, cargoWeight.getTareWeight());
		assertEquals("Net weight should match calculation", expectedNetWeight, cargoWeight.getNetWeight());

		// Verify the calculation
		int calculatedNet = cargoWeight.getGrossWeight() - cargoWeight.getTareWeight();
		assertEquals("Calculated net weight should match stored net weight",
			cargoWeight.getNetWeight(), calculatedNet);
	}

	@Test
	public void testLargeWeightValues() {
		int largeGross = Integer.MAX_VALUE;
		int largeTare = 1000000;
		int largeNet = Integer.MAX_VALUE - 1000000;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(largeGross, largeTare, largeNet);

		assertEquals("Large gross weight should be handled", largeGross, cargoWeight.getGrossWeight());
		assertEquals("Large tare weight should be handled", largeTare, cargoWeight.getTareWeight());
		assertEquals("Large net weight should be handled", largeNet, cargoWeight.getNetWeight());
	}

	@Test
	public void testMinimumWeightValues() {
		int minGross = Integer.MIN_VALUE;
		int minTare = Integer.MIN_VALUE;
		int minNet = Integer.MIN_VALUE;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(minGross, minTare, minNet);

		assertEquals("Minimum gross weight should be handled", minGross, cargoWeight.getGrossWeight());
		assertEquals("Minimum tare weight should be handled", minTare, cargoWeight.getTareWeight());
		assertEquals("Minimum net weight should be handled", minNet, cargoWeight.getNetWeight());
	}

	@Test
	public void testMultipleUpdates() {
		CargoWeightInfo cargoWeight = new CargoWeightInfo(1000, 100, 900);

		int[] grossValues = {1200, 1500, 1800, 2000};
		int[] tareValues = {120, 150, 180, 200};
		int[] netValues = {1080, 1350, 1620, 1800};

		for (int i = 0; i < grossValues.length; i++) {
			cargoWeight.setGrossWeight(grossValues[i]);
			cargoWeight.setTareWeight(tareValues[i]);
			cargoWeight.setNetWeight(netValues[i]);

			assertEquals("Gross weight should match after update " + i,
				grossValues[i], cargoWeight.getGrossWeight());
			assertEquals("Tare weight should match after update " + i,
				tareValues[i], cargoWeight.getTareWeight());
			assertEquals("Net weight should match after update " + i,
				netValues[i], cargoWeight.getNetWeight());
		}
	}

	@Test
	public void testObjectState() {
		int initialGross = 1000;
		int initialTare = 100;
		int initialNet = 900;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(initialGross, initialTare, initialNet);

		// Verify initial state
		assertEquals("Initial gross weight should match", initialGross, cargoWeight.getGrossWeight());
		assertEquals("Initial tare weight should match", initialTare, cargoWeight.getTareWeight());
		assertEquals("Initial net weight should match", initialNet, cargoWeight.getNetWeight());

		// Change one property
		int newGross = 1500;
		cargoWeight.setGrossWeight(newGross);

		// Verify only the changed property is updated
		assertEquals("Gross weight should be updated", newGross, cargoWeight.getGrossWeight());
		assertEquals("Tare weight should remain unchanged", initialTare, cargoWeight.getTareWeight());
		assertEquals("Net weight should remain unchanged", initialNet, cargoWeight.getNetWeight());

		// Change another property
		int newTare = 150;
		cargoWeight.setTareWeight(newTare);

		// Verify the state
		assertEquals("Gross weight should remain as updated", newGross, cargoWeight.getGrossWeight());
		assertEquals("Tare weight should be updated", newTare, cargoWeight.getTareWeight());
		assertEquals("Net weight should remain unchanged", initialNet, cargoWeight.getNetWeight());
	}

	@Test
	public void testCargoWeightEquality() {
		CargoWeightInfo weight1 = new CargoWeightInfo(1500, 200, 1300);
		CargoWeightInfo weight2 = new CargoWeightInfo(1500, 200, 1300);
		CargoWeightInfo weight3 = new CargoWeightInfo(1600, 200, 1400);

		// Note: This test assumes CargoWeightInfo doesn't override equals()
		// Test that objects with same data have same field values
		assertEquals("Same gross weights should match", weight1.getGrossWeight(), weight2.getGrossWeight());
		assertEquals("Same tare weights should match", weight1.getTareWeight(), weight2.getTareWeight());
		assertEquals("Same net weights should match", weight1.getNetWeight(), weight2.getNetWeight());

		// Test that objects with different data have different field values
		assertNotEquals("Different gross weights should not match",
			weight1.getGrossWeight(), weight3.getGrossWeight());
		assertNotEquals("Different net weights should not match",
			weight1.getNetWeight(), weight3.getNetWeight());
	}

	@Test
	public void testRealWorldScenario() {
		// Test a real-world cargo weighing scenario
		// Scenario: Loading a container
		int containerWeight = 2200; // Container tare weight in kg
		int totalWeight = 15000;     // Total weight including cargo in kg
		int cargoWeight = totalWeight - containerWeight; // 12800 kg

		CargoWeightInfo cargoWeightInfo = new CargoWeightInfo(totalWeight, containerWeight, cargoWeight);

		assertEquals("Total weight should match", totalWeight, cargoWeightInfo.getGrossWeight());
		assertEquals("Container weight should match", containerWeight, cargoWeightInfo.getTareWeight());
		assertEquals("Cargo weight should match", cargoWeight, cargoWeightInfo.getNetWeight());

		// Verify the calculation
		int calculatedCargo = cargoWeightInfo.getGrossWeight() - cargoWeightInfo.getTareWeight();
		assertEquals("Calculated cargo weight should match stored value",
			cargoWeightInfo.getNetWeight(), calculatedCargo);
	}

	@Test
	public void testEmptyContainerScenario() {
		// Test scenario with empty container (net weight = 0)
		int containerWeight = 2200;
		int totalWeight = 2200; // Same as container weight (empty)
		int cargoWeight = 0;    // No cargo

		CargoWeightInfo cargoWeightInfo = new CargoWeightInfo(totalWeight, containerWeight, cargoWeight);

		assertEquals("Empty container total weight should match tare",
			containerWeight, cargoWeightInfo.getGrossWeight());
		assertEquals("Container tare weight should match", containerWeight, cargoWeightInfo.getTareWeight());
		assertEquals("Cargo weight should be zero", 0, cargoWeightInfo.getNetWeight());
	}

	@Test
	public void testOverloadScenario() {
		// Test scenario where cargo exceeds normal limits
		int containerWeight = 2200;
		int overloadWeight = 25000; // Overloaded container
		int cargoWeight = overloadWeight - containerWeight; // 22800 kg

		CargoWeightInfo cargoWeightInfo = new CargoWeightInfo(overloadWeight, containerWeight, cargoWeight);

		assertEquals("Overload weight should be stored", overloadWeight, cargoWeightInfo.getGrossWeight());
		assertEquals("Container weight should match", containerWeight, cargoWeightInfo.getTareWeight());
		assertEquals("Heavy cargo weight should be stored", cargoWeight, cargoWeightInfo.getNetWeight());

		// Verify overload condition
		assertTrue("Should detect overload condition", cargoWeightInfo.getNetWeight() > 20000);
	}

	@Test
	public void testWeightConsistency() {
		// Test that weight values remain consistent across operations
		CargoWeightInfo cargoWeight = new CargoWeightInfo(1000, 100, 900);

		for (int i = 0; i < 100; i++) {
			int gross = 1000 + i * 10;
			int tare = 100 + i;
			int net = gross - tare;

			cargoWeight.setGrossWeight(gross);
			cargoWeight.setTareWeight(tare);
			cargoWeight.setNetWeight(net);

			assertEquals("Gross weight should be consistent at iteration " + i,
				gross, cargoWeight.getGrossWeight());
			assertEquals("Tare weight should be consistent at iteration " + i,
				tare, cargoWeight.getTareWeight());
			assertEquals("Net weight should be consistent at iteration " + i,
				net, cargoWeight.getNetWeight());
		}
	}

	@Test
	public void testToString() {
		CargoWeightInfo cargoWeight = new CargoWeightInfo(1500, 200, 1300);

		String toStringResult = cargoWeight.toString();

		assertNotNull("toString() should not return null", toStringResult);
		assertTrue("toString() should not be empty", toStringResult.length() > 0);
	}

	@Test
	public void testWeightUnitsConsistency() {
		// Test that all weights are in the same units (assuming kg)
		int grossKg = 1500;
		int tareKg = 200;
		int netKg = 1300;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(grossKg, tareKg, netKg);

		// All values should be in the same unit system
		assertTrue("Gross weight should be reasonable for kg", cargoWeight.getGrossWeight() > 0);
		assertTrue("Tare weight should be reasonable for kg", cargoWeight.getTareWeight() >= 0);
		assertTrue("Net weight should be reasonable for kg", cargoWeight.getNetWeight() >= 0);

		// Verify relationship between weights
		assertTrue("Gross weight should be >= tare weight in normal cases",
			cargoWeight.getGrossWeight() >= cargoWeight.getTareWeight() ||
				cargoWeight.getGrossWeight() < 0); // Allow negative values for error conditions
	}

	@Test
	public void testWeightPrecision() {
		// Test that integer weights maintain precision
		int preciseGross = 1234;
		int preciseTare = 567;
		int preciseNet = 667;

		CargoWeightInfo cargoWeight = new CargoWeightInfo(preciseGross, preciseTare, preciseNet);

		assertEquals("Precise gross weight should be maintained", preciseGross, cargoWeight.getGrossWeight());
		assertEquals("Precise tare weight should be maintained", preciseTare, cargoWeight.getTareWeight());
		assertEquals("Precise net weight should be maintained", preciseNet, cargoWeight.getNetWeight());
	}
}
