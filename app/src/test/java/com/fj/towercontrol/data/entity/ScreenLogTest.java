package com.fj.towercontrol.data.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import org.junit.Before;
import org.junit.Test;

/**
 * ScreenLog实体类测试
 *
 * <AUTHOR>
 */
public class ScreenLogTest {

	private ScreenLog screenLog;

	@Before
	public void setUp() {
		screenLog = new ScreenLog("12:00:00", "Test message");
	}

	@Test
	public void testConstructor() {
		String logTime = "14:30:25";
		String message = "System initialized";

		ScreenLog log = new ScreenLog(logTime, message);

		assertNotNull("ScreenLog should not be null", log);
		assertEquals("Log time should match", logTime, log.getLogTime());
		assertEquals("Message should match", message, log.getMsg());
	}

	@Test
	public void testGetLogTime() {
		assertEquals("Log time should match", "12:00:00", screenLog.getLogTime());
	}

	@Test
	public void testSetLogTime() {
		String newTime = "15:45:30";
		screenLog.setLogTime(newTime);

		assertEquals("Updated log time should match", newTime, screenLog.getLogTime());
	}

	@Test
	public void testGetMsg() {
		assertEquals("Message should match", "Test message", screenLog.getMsg());
	}

	@Test
	public void testSetMsg() {
		String newMessage = "Updated message";
		screenLog.setMsg(newMessage);

		assertEquals("Updated message should match", newMessage, screenLog.getMsg());
	}

	@Test
	public void testConstructorWithNullValues() {
		ScreenLog logWithNulls = new ScreenLog(null, null);

		assertNotNull("ScreenLog should not be null", logWithNulls);
		assertNull("Log time should be null", logWithNulls.getLogTime());
		assertNull("Message should be null", logWithNulls.getMsg());
	}

	@Test
	public void testConstructorWithEmptyValues() {
		ScreenLog logWithEmpty = new ScreenLog("", "");

		assertNotNull("ScreenLog should not be null", logWithEmpty);
		assertEquals("Log time should be empty", "", logWithEmpty.getLogTime());
		assertEquals("Message should be empty", "", logWithEmpty.getMsg());
	}

	@Test
	public void testSettersWithNullValues() {
		screenLog.setLogTime(null);
		screenLog.setMsg(null);

		assertNull("Log time should be null after setting", screenLog.getLogTime());
		assertNull("Message should be null after setting", screenLog.getMsg());
	}

	@Test
	public void testSettersWithEmptyValues() {
		screenLog.setLogTime("");
		screenLog.setMsg("");

		assertEquals("Log time should be empty after setting", "", screenLog.getLogTime());
		assertEquals("Message should be empty after setting", "", screenLog.getMsg());
	}

	@Test
	public void testLogTimeFormats() {
		String[] timeFormats = {
			"00:00:00",
			"23:59:59",
			"12:30:45",
			"09:05:15"
		};

		for (String timeFormat : timeFormats) {
			screenLog.setLogTime(timeFormat);
			assertEquals("Time format should be preserved: " + timeFormat,
				timeFormat, screenLog.getLogTime());
		}
	}

	@Test
	public void testMessageWithSpecialCharacters() {
		String specialMessage = "Error: Connection failed (timeout: 5000ms) - retry #3";
		screenLog.setMsg(specialMessage);

		assertEquals("Special characters should be preserved",
			specialMessage, screenLog.getMsg());
	}

	@Test
	public void testMessageWithUnicodeCharacters() {
		String unicodeMessage = "系统初始化成功 ✓";
		screenLog.setMsg(unicodeMessage);

		assertEquals("Unicode characters should be preserved",
			unicodeMessage, screenLog.getMsg());
	}

	@Test
	public void testLongMessage() {
		StringBuilder longMessage = new StringBuilder();
		for (int i = 0; i < 1000; i++) {
			longMessage.append("Long message part ").append(i).append(" ");
		}

		String longMsg = longMessage.toString();
		screenLog.setMsg(longMsg);

		assertEquals("Long message should be preserved", longMsg, screenLog.getMsg());
	}

	@Test
	public void testMultipleUpdates() {
		// Test multiple updates to ensure no side effects
		String[] times = {"10:00:00", "11:00:00", "12:00:00"};
		String[] messages = {"Message 1", "Message 2", "Message 3"};

		for (int i = 0; i < times.length; i++) {
			screenLog.setLogTime(times[i]);
			screenLog.setMsg(messages[i]);

			assertEquals("Time should match after update " + i, times[i], screenLog.getLogTime());
			assertEquals("Message should match after update " + i, messages[i], screenLog.getMsg());
		}
	}

	@Test
	public void testObjectState() {
		// Test that the object maintains its state correctly
		String originalTime = screenLog.getLogTime();
		String originalMsg = screenLog.getMsg();

		// Verify initial state
		assertEquals("Initial time should match", "12:00:00", originalTime);
		assertEquals("Initial message should match", "Test message", originalMsg);

		// Change one property
		screenLog.setLogTime("13:00:00");

		// Verify only the changed property is updated
		assertEquals("Time should be updated", "13:00:00", screenLog.getLogTime());
		assertEquals("Message should remain unchanged", "Test message", screenLog.getMsg());

		// Change the other property
		screenLog.setMsg("New message");

		// Verify both properties
		assertEquals("Time should remain as updated", "13:00:00", screenLog.getLogTime());
		assertEquals("Message should be updated", "New message", screenLog.getMsg());
	}

	@Test
	public void testEquality() {
		ScreenLog log1 = new ScreenLog("10:00:00", "Message");
		ScreenLog log2 = new ScreenLog("10:00:00", "Message");
		ScreenLog log3 = new ScreenLog("11:00:00", "Message");
		ScreenLog log4 = new ScreenLog("10:00:00", "Different Message");

		// Note: This test assumes ScreenLog doesn't override equals()
		// If equals() is implemented, these assertions would need to be updated
		assertNotNull("Log1 should not be null", log1);
		assertNotNull("Log2 should not be null", log2);
		assertNotNull("Log3 should not be null", log3);
		assertNotNull("Log4 should not be null", log4);

		// Test that objects with same data have same field values
		assertEquals("Same time should match", log1.getLogTime(), log2.getLogTime());
		assertEquals("Same message should match", log1.getMsg(), log2.getMsg());

		// Test that objects with different data have different field values
		assertNotEquals("Different times should not match", log1.getLogTime(), log3.getLogTime());
		assertNotEquals("Different messages should not match", log1.getMsg(), log4.getMsg());
	}

	@Test
	public void testToStringBehavior() {
		// Test that toString() doesn't throw exceptions
		String toStringResult = screenLog.toString();

		assertNotNull("toString() should not return null", toStringResult);
		assertTrue("toString() should not be empty", toStringResult.length() > 0);
	}

	@Test
	public void testWhitespaceHandling() {
		String timeWithSpaces = "  12:00:00  ";
		String messageWithSpaces = "  Test message  ";

		screenLog.setLogTime(timeWithSpaces);
		screenLog.setMsg(messageWithSpaces);

		// Test that whitespace is preserved (no trimming)
		assertEquals("Whitespace in time should be preserved", timeWithSpaces, screenLog.getLogTime());
		assertEquals("Whitespace in message should be preserved", messageWithSpaces, screenLog.getMsg());
	}

	@Test
	public void testNewlineHandling() {
		String messageWithNewlines = "Line 1\nLine 2\nLine 3";
		screenLog.setMsg(messageWithNewlines);

		assertEquals("Newlines should be preserved", messageWithNewlines, screenLog.getMsg());
	}

	@Test
	public void testTabHandling() {
		String messageWithTabs = "Column1\tColumn2\tColumn3";
		screenLog.setMsg(messageWithTabs);

		assertEquals("Tabs should be preserved", messageWithTabs, screenLog.getMsg());
	}
}
