package com.fj.towercontrol.data.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import org.junit.Test;

/**
 * VoiceAlarmType枚举类测试
 *
 * <AUTHOR>
 */
public class VoiceAlarmTypeTest {

	@Test
	public void testEnumValues() {
		// Test that all expected enum values exist
		VoiceAlarmType[] values = VoiceAlarmType.values();

		assertNotNull("Enum values should not be null", values);
		assertEquals("Should have 2 voice alarm types", 2, values.length);

		// Test specific enum values
		assertEquals("First enum should be LIFTING_ALARM", VoiceAlarmType.LIFTING_ALARM, values[0]);
		assertEquals("Second enum should be HANGING_ALARM", VoiceAlarmType.HANGING_ALARM, values[1]);
	}

	@Test
	public void testEnumOrdinals() {
		// Test ordinal values
		assertEquals("LIFTING_ALARM ordinal should be 0", 0, VoiceAlarmType.LIFTING_ALARM.ordinal());
		assertEquals("HANGING_ALARM ordinal should be 1", 1, VoiceAlarmType.HANGING_ALARM.ordinal());
	}

	@Test
	public void testEnumNames() {
		// Test enum names
		assertEquals("LIFTING_ALARM name should match", "LIFTING_ALARM", VoiceAlarmType.LIFTING_ALARM.name());
		assertEquals("HANGING_ALARM name should match", "HANGING_ALARM", VoiceAlarmType.HANGING_ALARM.name());
	}

	@Test
	public void testFileIndexValues() {
		// Test fileIndex property values
		assertEquals("LIFTING_ALARM fileIndex should be 1", 1, VoiceAlarmType.LIFTING_ALARM.getFileIndex());
		assertEquals("HANGING_ALARM fileIndex should be 2", 2, VoiceAlarmType.HANGING_ALARM.getFileIndex());
	}

	@Test
	public void testFromVoiceTypeValidValues() {
		// Test fromVoiceType with valid values
		assertEquals("Voice type 0 should return LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.fromVoiceType(0));
		assertEquals("Voice type 1 should return HANGING_ALARM",
			VoiceAlarmType.HANGING_ALARM, VoiceAlarmType.fromVoiceType(1));
	}

	@Test
	public void testFromVoiceTypeInvalidValues() {
		// Test fromVoiceType with invalid values - should default to LIFTING_ALARM
		assertEquals("Negative voice type should return LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.fromVoiceType(-1));
		assertEquals("Large voice type should return LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.fromVoiceType(100));
		assertEquals("Voice type 2 should return LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.fromVoiceType(2));
	}

	@Test
	public void testFromVoiceTypeBoundaryValues() {
		// Test boundary values
		assertEquals("Voice type 0 should return LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.fromVoiceType(0));
		assertEquals("Voice type 1 should return HANGING_ALARM",
			VoiceAlarmType.HANGING_ALARM, VoiceAlarmType.fromVoiceType(1));
		assertEquals("Voice type 2 should default to LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.fromVoiceType(2));
	}

	@Test
	public void testValueOf() {
		// Test valueOf method
		assertEquals("valueOf LIFTING_ALARM should work",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.valueOf("LIFTING_ALARM"));
		assertEquals("valueOf HANGING_ALARM should work",
			VoiceAlarmType.HANGING_ALARM, VoiceAlarmType.valueOf("HANGING_ALARM"));
	}

	@Test(expected = IllegalArgumentException.class)
	public void testValueOfInvalidName() {
		// Test valueOf with invalid name should throw exception
		VoiceAlarmType.valueOf("INVALID_ALARM_TYPE");
	}

	@Test(expected = NullPointerException.class)
	public void testValueOfNullName() {
		// Test valueOf with null should throw exception
		VoiceAlarmType.valueOf(null);
	}

	@Test
	public void testEnumEquality() {
		// Test enum equality
		assertEquals("Same enum values should be equal",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.LIFTING_ALARM);
		assertNotEquals("Different enum values should not be equal",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.HANGING_ALARM);

		// Test with valueOf
		assertEquals("valueOf result should equal direct reference",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.valueOf("LIFTING_ALARM"));
	}

	@Test
	public void testEnumToString() {
		// Test toString method (should return name by default)
		assertEquals("LIFTING_ALARM toString should match name",
			"LIFTING_ALARM", VoiceAlarmType.LIFTING_ALARM.toString());
		assertEquals("HANGING_ALARM toString should match name",
			"HANGING_ALARM", VoiceAlarmType.HANGING_ALARM.toString());
	}

	@Test
	public void testEnumHashCode() {
		// Test hashCode consistency
		int hash1 = VoiceAlarmType.LIFTING_ALARM.hashCode();
		int hash2 = VoiceAlarmType.LIFTING_ALARM.hashCode();
		assertEquals("Hash codes should be consistent", hash1, hash2);

		// Different enums should have different hash codes (usually)
		assertNotEquals("Different enums should have different hash codes",
			VoiceAlarmType.LIFTING_ALARM.hashCode(), VoiceAlarmType.HANGING_ALARM.hashCode());
	}

	@Test
	public void testEnumComparison() {
		// Test enum comparison (based on ordinal)
		assertTrue("LIFTING_ALARM should be less than HANGING_ALARM",
			VoiceAlarmType.LIFTING_ALARM.compareTo(VoiceAlarmType.HANGING_ALARM) < 0);
		assertTrue("HANGING_ALARM should be greater than LIFTING_ALARM",
			VoiceAlarmType.HANGING_ALARM.compareTo(VoiceAlarmType.LIFTING_ALARM) > 0);
		assertEquals("Same enum should compare equal",
			0, VoiceAlarmType.LIFTING_ALARM.compareTo(VoiceAlarmType.LIFTING_ALARM));
	}

	@Test
	public void testBusinessLogicMapping() {
		// Test the business logic mapping described in comments
		// 0:吊运中告警 -> LIFTING_ALARM
		// 1:空钩告警 -> HANGING_ALARM

		VoiceAlarmType liftingAlarm = VoiceAlarmType.fromVoiceType(0);
		VoiceAlarmType hangingAlarm = VoiceAlarmType.fromVoiceType(1);

		assertEquals("Voice type 0 should map to LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, liftingAlarm);
		assertEquals("Voice type 1 should map to HANGING_ALARM",
			VoiceAlarmType.HANGING_ALARM, hangingAlarm);

		// Test file indices match expected values
		assertEquals("LIFTING_ALARM should use file index 1", 1, liftingAlarm.getFileIndex());
		assertEquals("HANGING_ALARM should use file index 2", 2, hangingAlarm.getFileIndex());
	}

	@Test
	public void testFileIndexUniqueness() {
		// Test that file indices are unique
		VoiceAlarmType[] values = VoiceAlarmType.values();

		for (int i = 0; i < values.length; i++) {
			for (int j = i + 1; j < values.length; j++) {
				assertNotEquals("File indices should be unique",
					values[i].getFileIndex(), values[j].getFileIndex());
			}
		}
	}

	@Test
	public void testFileIndexPositive() {
		// Test that all file indices are positive (starting from 1)
		for (VoiceAlarmType alarmType : VoiceAlarmType.values()) {
			assertTrue("File index should be positive for " + alarmType,
				alarmType.getFileIndex() > 0);
		}
	}

	@Test
	public void testEnumInSwitch() {
		// Test enum usage in switch statement
		for (VoiceAlarmType alarmType : VoiceAlarmType.values()) {
			String description = getAlarmDescription(alarmType);
			assertNotNull("Description should not be null for " + alarmType, description);
			assertFalse("Description should not be empty for " + alarmType, description.isEmpty());
		}
	}

	@Test
	public void testAlarmTypeBusinessScenarios() {
		// Test real-world business scenarios

		// Scenario 1: Crane is lifting cargo
		VoiceAlarmType liftingScenario = VoiceAlarmType.fromVoiceType(0);
		assertEquals("Lifting scenario should use LIFTING_ALARM",
			VoiceAlarmType.LIFTING_ALARM, liftingScenario);
		assertEquals("Lifting alarm should use file 1", 1, liftingScenario.getFileIndex());

		// Scenario 2: Crane hook is empty
		VoiceAlarmType hangingScenario = VoiceAlarmType.fromVoiceType(1);
		assertEquals("Hanging scenario should use HANGING_ALARM",
			VoiceAlarmType.HANGING_ALARM, hangingScenario);
		assertEquals("Hanging alarm should use file 2", 2, hangingScenario.getFileIndex());
	}

	@Test
	public void testFromVoiceTypeConsistency() {
		// Test that fromVoiceType is consistent across multiple calls
		for (int i = 0; i < 10; i++) {
			VoiceAlarmType result1 = VoiceAlarmType.fromVoiceType(0);
			VoiceAlarmType result2 = VoiceAlarmType.fromVoiceType(0);
			assertEquals("Multiple calls should return same result", result1, result2);

			VoiceAlarmType result3 = VoiceAlarmType.fromVoiceType(1);
			VoiceAlarmType result4 = VoiceAlarmType.fromVoiceType(1);
			assertEquals("Multiple calls should return same result", result3, result4);
		}
	}

	@Test
	public void testEnumIteration() {
		// Test iterating over all enum values
		int count = 0;
		for (VoiceAlarmType alarmType : VoiceAlarmType.values()) {
			assertNotNull("Enum value should not be null", alarmType);
			assertTrue("File index should be valid", alarmType.getFileIndex() > 0);
			count++;
		}
		assertEquals("Should iterate over all 2 values", 2, count);
	}

	@Test
	public void testEnumConstantProperties() {
		// Test that enum constants are properly defined
		assertNotNull("LIFTING_ALARM should be defined", VoiceAlarmType.LIFTING_ALARM);
		assertNotNull("HANGING_ALARM should be defined", VoiceAlarmType.HANGING_ALARM);

		// Test that they are different instances
		assertNotSame("LIFTING_ALARM and HANGING_ALARM should be different instances",
			VoiceAlarmType.LIFTING_ALARM, VoiceAlarmType.HANGING_ALARM);
	}

	@Test
	public void testEnumSerialization() {
		// Test that enum can be serialized/deserialized properly
		for (VoiceAlarmType alarmType : VoiceAlarmType.values()) {
			// Test name-based serialization
			String name = alarmType.name();
			VoiceAlarmType deserializedByName = VoiceAlarmType.valueOf(name);
			assertEquals("Name-based serialization should preserve enum value", alarmType, deserializedByName);
			assertEquals("File index should be preserved",
				alarmType.getFileIndex(), deserializedByName.getFileIndex());
		}
	}

	@Test
	public void testFromVoiceTypeEdgeCases() {
		// Test edge cases for fromVoiceType
		int[] testValues = {-100, -1, 0, 1, 2, 3, 100, Integer.MAX_VALUE, Integer.MIN_VALUE};

		for (int testValue : testValues) {
			VoiceAlarmType result = VoiceAlarmType.fromVoiceType(testValue);
			assertNotNull("fromVoiceType should never return null for " + testValue, result);

			if (testValue == 1) {
				assertEquals("Voice type 1 should return HANGING_ALARM",
					VoiceAlarmType.HANGING_ALARM, result);
			} else {
				assertEquals("Voice type " + testValue + " should return LIFTING_ALARM",
					VoiceAlarmType.LIFTING_ALARM, result);
			}
		}
	}

	@Test
	public void testEnumThreadSafety() {
		// Test that enum operations are thread-safe (basic test)
		Runnable task = () -> {
			for (int i = 0; i < 1000; i++) {
				VoiceAlarmType type1 = VoiceAlarmType.fromVoiceType(i % 2);
				VoiceAlarmType type2 = VoiceAlarmType.fromVoiceType((i + 1) % 2);

				assertNotNull("fromVoiceType should work in multithreaded environment", type1);
				assertNotNull("fromVoiceType should work in multithreaded environment", type2);

				assertTrue("File index should be valid", type1.getFileIndex() > 0);
				assertTrue("File index should be valid", type2.getFileIndex() > 0);
			}
		};

		Thread thread1 = new Thread(task);
		Thread thread2 = new Thread(task);

		thread1.start();
		thread2.start();

		try {
			thread1.join();
			thread2.join();
		} catch (InterruptedException e) {
			fail("Thread test was interrupted");
		}
	}

	// Helper method for testing business logic
	private String getAlarmDescription(VoiceAlarmType alarmType) {
		switch (alarmType) {
			case LIFTING_ALARM:
				return "吊物吊运告警";
			case HANGING_ALARM:
				return "空钩告警";
			default:
				return "未知告警类型";
		}
	}
}
