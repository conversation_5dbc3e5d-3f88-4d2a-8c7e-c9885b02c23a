package com.fj.towercontrol.data.entity

import org.junit.Assert.*
import org.junit.Test

/**
 * YongMaoRawData单元测试
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
class YongMaoRawDataTest {

    @Test
    fun testParseWithValidData() {
        // 给定的测试数据
        val inputHex = "23 01 52 00 00 00 00 00 2f 04 00 00 00 00 00 00 16 0a 00 00 08 00 00 00 64 00 00 00 00 00 00 00 a0 0f 00 00 00 00 00 00 ef 00 00 00 2f 01 00 00 45 00 00 00 1e 00 00 00 d4 33 00 00 93 f8 ff ff 4e 00 00 00 50 00 00 00 35 00 00 00 04 00 3f 00 ce 31"
        val inputBytes = hexStringToByteArray(inputHex)

        // 调用parse方法
        val result = YongMaoRawData.parse(inputBytes)

        // 验证解析结果
        // 从第4个字节开始解析，使用小端字节序
        
        // rotateAngle: 00 00 00 00 (小端) = 0 * 0.01 = 0.0f
        assertEquals(0.0f, result.rotateAngle, 0.001f)
        
        // trolleyAmplitude: 2f 04 00 00 (小端) = 1071 * 0.01 = 10.71f
        assertEquals(10.71f, result.trolleyAmplitude, 0.001f)
        
        // boomPitchAngle: 00 00 00 00 (小端) = 0 * 0.01 = 0.0f
        assertEquals(0.0f, result.boomPitchAngle, 0.001f)
        
        // hookHeight: 16 0a 00 00 (小端) = 2582 * 0.01 = 25.82f
        assertEquals(25.82f, result.hookHeight, 0.001f)
        
        // hookWeight: 08 00 00 00 (小端) = 8 * 0.01 = 0.08f
        assertEquals(0.08f, result.hookWeight, 0.001f)
        
        // liftingMomentPercent: 64 00 00 00 (小端) = 100 * 0.01 = 1.0f
        assertEquals(1.0f, result.liftingMomentPercent, 0.001f)
        
        // windSpeed: 00 00 00 00 (小端) = 0 * 0.01 = 0.0f
        assertEquals(0.0f, result.windSpeed, 0.001f)
        
        // jibLength: a0 0f 00 00 (小端) = 4000 * 0.01 = 40.0f
        assertEquals(40.0f, result.jibLength, 0.001f)
        
        // angleSensor: 00 00 00 00 (小端) = 0 * 0.01 = 0.0f
        assertEquals(0.0f, result.angleSensor, 0.001f)
        
        // heightSensor: ef 00 00 00 (小端) = 239 * 0.01 = 2.39f
        assertEquals(2.39f, result.heightSensor, 0.001f)
        
        // amplitudeSensor: 2f 01 00 00 (小端) = 303 * 0.01 = 3.03f
        assertEquals(3.03f, result.amplitudeSensor, 0.001f)
        
        // weightSensor: 45 00 00 00 (小端) = 69 * 0.01 = 0.69f
        assertEquals(0.69f, result.weightSensor, 0.001f)
        
        // angleCalibrationFactor: 1e 00 00 00 (小端) = 30 * 0.01 = 0.3f
        assertEquals(0.3f, result.angleCalibrationFactor, 0.001f)
        
        // heightCalibrationFactor: d4 33 00 00 (小端) = 13268 * 0.01 = 132.68f
        assertEquals(132.68f, result.heightCalibrationFactor, 0.001f)
        
        // amplitudeCalibrationFactor: 93 f8 ff ff (小端) = -1901 * 0.01 = -19.01f
        assertEquals(-19.01f, result.amplitudeCalibrationFactor, 0.001f)
        
        // weightCalibrationFactor1: 4e 00 00 00 (小端) = 78 * 0.01 = 0.78f
        assertEquals(0.78f, result.weightCalibrationFactor1, 0.001f)
        
        // weightCalibrationFactor2: 50 00 00 00 (小端) = 80 * 0.01 = 0.8f
        assertEquals(0.8f, result.weightCalibrationFactor2, 0.001f)
        
        // weightCalibrationFactor3: 35 00 00 00 (小端) = 53 * 0.01 = 0.53f
        assertEquals(0.53f, result.weightCalibrationFactor3, 0.001f)
        
        // hookMultiple: 04 00 (小端) = 4 * 0.01 = 0.04f
        assertEquals(0.04f, result.hookMultiple, 0.001f)
        
        // statusA: 3f 00 (小端) = 63
        assertEquals(63, result.statusA)
    }

    @Test
    fun testParseWithInsufficientData() {
        // 测试数据长度不足的情况
        val shortData = byteArrayOf(0x01, 0x02, 0x03)
        val result = YongMaoRawData.parse(shortData)
        
        // 应该返回默认值的对象
        assertEquals(0.0f, result.rotateAngle, 0.001f)
        assertEquals(0.0f, result.trolleyAmplitude, 0.001f)
        assertEquals(0.0f, result.boomPitchAngle, 0.001f)
        assertEquals(0.0f, result.hookHeight, 0.001f)
        assertEquals(0.0f, result.hookWeight, 0.001f)
        assertEquals(0.0f, result.liftingMomentPercent, 0.001f)
        assertEquals(0.0f, result.windSpeed, 0.001f)
        assertEquals(0.0f, result.jibLength, 0.001f)
        assertEquals(0.0f, result.angleSensor, 0.001f)
        assertEquals(0.0f, result.heightSensor, 0.001f)
        assertEquals(0.0f, result.amplitudeSensor, 0.001f)
        assertEquals(0.0f, result.weightSensor, 0.001f)
        assertEquals(0.0f, result.angleCalibrationFactor, 0.001f)
        assertEquals(0.0f, result.heightCalibrationFactor, 0.001f)
        assertEquals(0.0f, result.amplitudeCalibrationFactor, 0.001f)
        assertEquals(0.0f, result.weightCalibrationFactor1, 0.001f)
        assertEquals(0.0f, result.weightCalibrationFactor2, 0.001f)
        assertEquals(0.0f, result.weightCalibrationFactor3, 0.001f)
        assertEquals(0.0f, result.hookMultiple, 0.001f)
        assertEquals(0, result.statusA)
    }

    @Test
    fun testParseWithEmptyData() {
        // 测试空数据的情况
        val emptyData = byteArrayOf()
        val result = YongMaoRawData.parse(emptyData)
        
        // 应该返回默认值的对象
        assertEquals(0.0f, result.rotateAngle, 0.001f)
        assertEquals(0.0f, result.trolleyAmplitude, 0.001f)
        assertEquals(0, result.statusA)
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    private fun hexStringToByteArray(hex: String): ByteArray {
        val cleanHex = hex.replace(" ", "")
        val len = cleanHex.length
        val data = ByteArray(len / 2)
        for (i in 0 until len step 2) {
            data[i / 2] = ((Character.digit(cleanHex[i], 16) shl 4) + Character.digit(cleanHex[i + 1], 16)).toByte()
        }
        return data
    }
}
