package com.fj.towercontrol.data.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

/**
 * Position实体类测试
 *
 * <AUTHOR>
 */
public class PositionTest {

	@Test
	public void testConstructorWithLatLng() {
		double latitude = 39.9042;
		double longitude = 116.4074;

		Position position = new Position(latitude, longitude, 0.0);

		assertNotNull("Position should not be null", position);
		assertEquals("Latitude should match", latitude, position.getLatitude(), 0.000001);
		assertEquals("Longitude should match", longitude, position.getLongitude(), 0.000001);
		assertEquals("Default altitude should be 0", 0.0, position.getAltitude(), 0.000001);
	}

	@Test
	public void testConstructorWithLatLngAlt() {
		double latitude = 39.9042;
		double longitude = 116.4074;
		double altitude = 45.5;

		Position position = new Position(latitude, longitude, altitude);

		assertNotNull("Position should not be null", position);
		assertEquals("Latitude should match", latitude, position.getLatitude(), 0.000001);
		assertEquals("Longitude should match", longitude, position.getLongitude(), 0.000001);
		assertEquals("Altitude should match", altitude, position.getAltitude(), 0.000001);
	}

	@Test
	public void testBeijingCoordinates() {
		// Test with Beijing coordinates
		double beijingLat = 39.9042;
		double beijingLng = 116.4074;
		double beijingAlt = 43.5;

		Position beijing = new Position(beijingLat, beijingLng, beijingAlt);

		assertEquals("Beijing latitude should match", beijingLat, beijing.getLatitude(), 0.000001);
		assertEquals("Beijing longitude should match", beijingLng, beijing.getLongitude(), 0.000001);
		assertEquals("Beijing altitude should match", beijingAlt, beijing.getAltitude(), 0.000001);
	}

	@Test
	public void testShanghaiCoordinates() {
		// Test with Shanghai coordinates
		double shanghaiLat = 31.2304;
		double shanghaiLng = 121.4737;
		double shanghaiAlt = 4.0;

		Position shanghai = new Position(shanghaiLat, shanghaiLng, shanghaiAlt);

		assertEquals("Shanghai latitude should match", shanghaiLat, shanghai.getLatitude(), 0.000001);
		assertEquals("Shanghai longitude should match", shanghaiLng, shanghai.getLongitude(), 0.000001);
		assertEquals("Shanghai altitude should match", shanghaiAlt, shanghai.getAltitude(), 0.000001);
	}

	@Test
	public void testZeroCoordinates() {
		// Test with zero coordinates (null island)
		double zeroLat = 0.0;
		double zeroLng = 0.0;
		double zeroAlt = 0.0;

		Position zeroPosition = new Position(zeroLat, zeroLng, zeroAlt);

		assertEquals("Zero latitude should be handled", zeroLat, zeroPosition.getLatitude(), 0.000001);
		assertEquals("Zero longitude should be handled", zeroLng, zeroPosition.getLongitude(), 0.000001);
		assertEquals("Zero altitude should be handled", zeroAlt, zeroPosition.getAltitude(), 0.000001);
	}

	@Test
	public void testNegativeCoordinates() {
		// Test with negative coordinates (southern/western hemispheres)
		double southLat = -33.8688; // Sydney
		double westLng = -151.2093; // Sydney
		double negativeAlt = -10.0;  // Below sea level

		Position negativePosition = new Position(southLat, westLng, negativeAlt);

		assertEquals("Negative latitude should be handled", southLat, negativePosition.getLatitude(), 0.000001);
		assertEquals("Negative longitude should be handled", westLng, negativePosition.getLongitude(), 0.000001);
		assertEquals("Negative altitude should be handled", negativeAlt, negativePosition.getAltitude(), 0.000001);
	}

	@Test
	public void testLatitudeBoundaries() {
		// Test latitude boundaries (-90 to 90)
		double maxLat = 90.0;   // North Pole
		double minLat = -90.0;  // South Pole
		double lng = 0.0;

		Position northPole = new Position(maxLat, lng, 0.0);
		Position southPole = new Position(minLat, lng, 0.0);

		assertEquals("Max latitude should be handled", maxLat, northPole.getLatitude(), 0.000001);
		assertEquals("Min latitude should be handled", minLat, southPole.getLatitude(), 0.000001);
	}

	@Test
	public void testLongitudeBoundaries() {
		// Test longitude boundaries (-180 to 180)
		double lat = 0.0;
		double maxLng = 180.0;  // International Date Line (East)
		double minLng = -180.0; // International Date Line (West)

		Position eastDateLine = new Position(lat, maxLng, 0.0);
		Position westDateLine = new Position(lat, minLng, 0.0);

		assertEquals("Max longitude should be handled", maxLng, eastDateLine.getLongitude(), 0.000001);
		assertEquals("Min longitude should be handled", minLng, westDateLine.getLongitude(), 0.000001);
	}

	@Test
	public void testHighPrecisionCoordinates() {
		// Test with high precision coordinates
		double preciseLat = 39.904211123456789;
		double preciseLng = 116.407395987654321;
		double preciseAlt = 45.123456789;

		Position precisePosition = new Position(preciseLat, preciseLng, preciseAlt);

		assertEquals("High precision latitude should be preserved",
			preciseLat, precisePosition.getLatitude(), 0.000000000001);
		assertEquals("High precision longitude should be preserved",
			preciseLng, precisePosition.getLongitude(), 0.000000000001);
		assertEquals("High precision altitude should be preserved",
			preciseAlt, precisePosition.getAltitude(), 0.000000000001);
	}

	@Test
	public void testExtremeAltitudes() {
		// Test with extreme altitude values
		double lat = 39.9042;
		double lng = 116.4074;
		double highAlt = 8848.86;    // Mount Everest height
		double lowAlt = -11034.0;    // Mariana Trench depth

		Position highPosition = new Position(lat, lng, highAlt);
		Position lowPosition = new Position(lat, lng, lowAlt);

		assertEquals("High altitude should be handled", highAlt, highPosition.getAltitude(), 0.01);
		assertEquals("Low altitude should be handled", lowAlt, lowPosition.getAltitude(), 0.01);
	}

	@Test
	public void testPositionEquality() {
		double lat = 39.9042;
		double lng = 116.4074;
		double alt = 45.5;

		Position position1 = new Position(lat, lng, alt);
		Position position2 = new Position(lat, lng, alt);
		Position position3 = new Position(lat + 0.001, lng, alt);

		// Test that positions with same coordinates have same field values
		assertEquals("Same latitudes should match", position1.getLatitude(), position2.getLatitude(), 0.000001);
		assertEquals("Same longitudes should match", position1.getLongitude(), position2.getLongitude(), 0.000001);
		assertEquals("Same altitudes should match", position1.getAltitude(), position2.getAltitude(), 0.000001);

		// Test that positions with different coordinates have different field values
		assertNotEquals("Different latitudes should not match",
			position1.getLatitude(), position3.getLatitude(), 0.000001);
	}

	@Test
	public void testPositionToString() {
		Position position = new Position(39.9042, 116.4074, 45.5);

		String toStringResult = position.toString();

		assertNotNull("toString() should not return null", toStringResult);
		assertTrue("toString() should not be empty", toStringResult.length() > 0);
	}

	@Test
	public void testPositionImmutability() {
		// Test that Position is immutable (data class with val properties)
		double originalLat = 39.9042;
		double originalLng = 116.4074;
		double originalAlt = 45.5;

		Position position = new Position(originalLat, originalLng, originalAlt);

		// Verify values
		assertEquals("Initial latitude should match", originalLat, position.getLatitude(), 0.000001);
		assertEquals("Initial longitude should match", originalLng, position.getLongitude(), 0.000001);
		assertEquals("Initial altitude should match", originalAlt, position.getAltitude(), 0.000001);

		// Since Position is a data class with val properties, we can't modify the values
		// This test verifies that the values remain constant
		assertEquals("Latitude should remain constant", originalLat, position.getLatitude(), 0.000001);
		assertEquals("Longitude should remain constant", originalLng, position.getLongitude(), 0.000001);
		assertEquals("Altitude should remain constant", originalAlt, position.getAltitude(), 0.000001);
	}

	@Test
	public void testPositionWithInfiniteValues() {
		// Test with infinite values
		Position posInfPosition = new Position(Double.POSITIVE_INFINITY, 116.4074, 45.5);
		Position negInfPosition = new Position(39.9042, Double.NEGATIVE_INFINITY, 45.5);
		Position infAltPosition = new Position(39.9042, 116.4074, Double.POSITIVE_INFINITY);

		assertEquals("Positive infinity latitude should be preserved",
			Double.POSITIVE_INFINITY, posInfPosition.getLatitude(), 0.0);
		assertEquals("Negative infinity longitude should be preserved",
			Double.NEGATIVE_INFINITY, negInfPosition.getLongitude(), 0.0);
		assertEquals("Infinite altitude should be preserved",
			Double.POSITIVE_INFINITY, infAltPosition.getAltitude(), 0.0);
	}

	@Test
	public void testPositionWithNaNValues() {
		// Test with NaN values
		Position nanLatPosition = new Position(Double.NaN, 116.4074, 45.5);
		Position nanLngPosition = new Position(39.9042, Double.NaN, 45.5);
		Position nanAltPosition = new Position(39.9042, 116.4074, Double.NaN);

		assertTrue("NaN latitude should be preserved", Double.isNaN(nanLatPosition.getLatitude()));
		assertTrue("NaN longitude should be preserved", Double.isNaN(nanLngPosition.getLongitude()));
		assertTrue("NaN altitude should be preserved", Double.isNaN(nanAltPosition.getAltitude()));
	}

	@Test
	public void testRealWorldConstructionSite() {
		// Test with real-world construction site coordinates
		double siteLat = 39.9088;    // Construction site latitude
		double siteLng = 116.3974;   // Construction site longitude
		double craneHeight = 85.0;   // Tower crane height in meters

		Position cranePosition = new Position(siteLat, siteLng, craneHeight);

		assertEquals("Construction site latitude should match", siteLat, cranePosition.getLatitude(), 0.000001);
		assertEquals("Construction site longitude should match", siteLng, cranePosition.getLongitude(), 0.000001);
		assertEquals("Crane height should match", craneHeight, cranePosition.getAltitude(), 0.000001);

		// Verify coordinates are within reasonable ranges for construction
		assertTrue("Site latitude should be reasonable", Math.abs(cranePosition.getLatitude()) <= 90.0);
		assertTrue("Site longitude should be reasonable", Math.abs(cranePosition.getLongitude()) <= 180.0);
		assertTrue("Crane height should be positive", cranePosition.getAltitude() > 0);
	}

	@Test
	public void testMultipleConstructionSites() {
		// Test multiple construction sites
		Position[] sites = {
			new Position(39.9088, 116.3974, 85.0),  // Beijing site 1
			new Position(39.9156, 116.4074, 92.5),  // Beijing site 2
			new Position(31.2304, 121.4737, 78.0),  // Shanghai site
			new Position(22.3193, 114.1694, 65.5)   // Hong Kong site
		};

		for (int i = 0; i < sites.length; i++) {
			Position site = sites[i];
			assertNotNull("Site " + i + " should not be null", site);
			assertTrue("Site " + i + " latitude should be valid",
				Math.abs(site.getLatitude()) <= 90.0);
			assertTrue("Site " + i + " longitude should be valid",
				Math.abs(site.getLongitude()) <= 180.0);
			assertTrue("Site " + i + " altitude should be reasonable",
				site.getAltitude() > 0 && site.getAltitude() < 200);
		}
	}

	@Test
	public void testPositionPrecisionConsistency() {
		// Test that precision is maintained across multiple operations
		double baseLat = 39.904211;
		double baseLng = 116.407395;
		double baseAlt = 45.123456;

		for (int i = 0; i < 100; i++) {
			double lat = baseLat + (i * 0.000001);
			double lng = baseLng + (i * 0.000001);
			double alt = baseAlt + (i * 0.001);

			Position position = new Position(lat, lng, alt);

			assertEquals("Latitude precision should be maintained at iteration " + i,
				lat, position.getLatitude(), 0.0000000001);
			assertEquals("Longitude precision should be maintained at iteration " + i,
				lng, position.getLongitude(), 0.0000000001);
			assertEquals("Altitude precision should be maintained at iteration " + i,
				alt, position.getAltitude(), 0.0000000001);
		}
	}

	@Test
	public void testPositionDistanceCalculation() {
		// Test positions for distance calculation scenarios
		Position beijing = new Position(39.9042, 116.4074, 45.0);
		Position shanghai = new Position(31.2304, 121.4737, 4.0);

		// Verify positions are different
		assertNotEquals("Beijing and Shanghai latitudes should be different",
			beijing.getLatitude(), shanghai.getLatitude(), 0.001);
		assertNotEquals("Beijing and Shanghai longitudes should be different",
			beijing.getLongitude(), shanghai.getLongitude(), 0.001);
		assertNotEquals("Beijing and Shanghai altitudes should be different",
			beijing.getAltitude(), shanghai.getAltitude(), 0.001);

		// Verify coordinates are reasonable for distance calculation
		assertTrue("Latitude difference should be significant",
			Math.abs(beijing.getLatitude() - shanghai.getLatitude()) > 5.0);
		assertTrue("Longitude difference should be significant",
			Math.abs(beijing.getLongitude() - shanghai.getLongitude()) > 3.0);
	}

	@Test
	public void testPositionHashCodeConsistency() {
		// Test that positions with same coordinates have consistent hash codes
		Position position1 = new Position(39.9042, 116.4074, 45.5);
		Position position2 = new Position(39.9042, 116.4074, 45.5);

		// If Position overrides hashCode(), these should be equal
		// This is a basic test to ensure hashCode() doesn't throw exceptions
		int hash1 = position1.hashCode();
		int hash2 = position2.hashCode();

		// Hash codes should be consistent for same object
		assertEquals("Hash code should be consistent", hash1, position1.hashCode());
		assertEquals("Hash code should be consistent", hash2, position2.hashCode());
	}
}
