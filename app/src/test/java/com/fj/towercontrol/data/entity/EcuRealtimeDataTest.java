package com.fj.towercontrol.data.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

/**
 * EcuRealtimeData实体类测试
 *
 * <AUTHOR>
 */
public class EcuRealtimeDataTest {

	@Test
	public void testDefaultConstructor() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		assertNotNull("EcuRealtimeData should not be null", ecuData);
		assertEquals("Default modeParent should be 0", 0, ecuData.getModeParent());
		assertEquals("Default modeChild should be 0", 0, ecuData.getModeChild());
		assertEquals("Default craneStatus should be 0", 0, ecuData.getCraneStatus());
	}

	@Test
	public void testModeParentGetterSetter() {
		EcuRealtimeData ecuData = new EcuRealtimeData();
		int testModeParent = 5;

		ecuData.setModeParent(testModeParent);
		int retrievedModeParent = ecuData.getModeParent();

		assertEquals("ModeParent should match", testModeParent, retrievedModeParent);
	}

	@Test
	public void testModeChildGetterSetter() {
		EcuRealtimeData ecuData = new EcuRealtimeData();
		int testModeChild = 3;

		ecuData.setModeChild(testModeChild);
		int retrievedModeChild = ecuData.getModeChild();

		assertEquals("ModeChild should match", testModeChild, retrievedModeChild);
	}

	@Test
	public void testCraneStatusGetterSetter() {
		EcuRealtimeData ecuData = new EcuRealtimeData();
		int testCraneStatus = 7;

		ecuData.setCraneStatus(testCraneStatus);
		int retrievedCraneStatus = ecuData.getCraneStatus();

		assertEquals("CraneStatus should match", testCraneStatus, retrievedCraneStatus);
	}

	@Test
	public void testModeParentValues() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test various mode parent values
		int[] modeParentValues = {0, 1, 2, 3, 4, 5, 10, 15, 20};

		for (int modeParent : modeParentValues) {
			ecuData.setModeParent(modeParent);
			assertEquals("ModeParent " + modeParent + " should be stored correctly",
				modeParent, ecuData.getModeParent());
		}
	}

	@Test
	public void testModeChildValues() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test various mode child values
		int[] modeChildValues = {0, 1, 2, 3, 4, 5, 10, 15, 20};

		for (int modeChild : modeChildValues) {
			ecuData.setModeChild(modeChild);
			assertEquals("ModeChild " + modeChild + " should be stored correctly",
				modeChild, ecuData.getModeChild());
		}
	}

	@Test
	public void testCraneStatusValues() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test various crane status values
		int[] craneStatusValues = {0, 1, 2, 3, 4, 5, 10, 15, 20};

		for (int craneStatus : craneStatusValues) {
			ecuData.setCraneStatus(craneStatus);
			assertEquals("CraneStatus " + craneStatus + " should be stored correctly",
				craneStatus, ecuData.getCraneStatus());
		}
	}

	@Test
	public void testNegativeValues() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test negative values
		ecuData.setModeParent(-1);
		ecuData.setModeChild(-5);
		ecuData.setCraneStatus(-10);

		assertEquals("Negative modeParent should be handled", -1, ecuData.getModeParent());
		assertEquals("Negative modeChild should be handled", -5, ecuData.getModeChild());
		assertEquals("Negative craneStatus should be handled", -10, ecuData.getCraneStatus());
	}

	@Test
	public void testLargeValues() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test large values
		int largeModeParent = 999999;
		int largeModeChild = 888888;
		int largeCraneStatus = 777777;

		ecuData.setModeParent(largeModeParent);
		ecuData.setModeChild(largeModeChild);
		ecuData.setCraneStatus(largeCraneStatus);

		assertEquals("Large modeParent should be handled", largeModeParent, ecuData.getModeParent());
		assertEquals("Large modeChild should be handled", largeModeChild, ecuData.getModeChild());
		assertEquals("Large craneStatus should be handled", largeCraneStatus, ecuData.getCraneStatus());
	}

	@Test
	public void testExtremeValues() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test extreme integer values
		ecuData.setModeParent(Integer.MAX_VALUE);
		ecuData.setModeChild(Integer.MIN_VALUE);
		ecuData.setCraneStatus(Integer.MAX_VALUE);

		assertEquals("Max integer modeParent should be handled",
			Integer.MAX_VALUE, ecuData.getModeParent());
		assertEquals("Min integer modeChild should be handled",
			Integer.MIN_VALUE, ecuData.getModeChild());
		assertEquals("Max integer craneStatus should be handled",
			Integer.MAX_VALUE, ecuData.getCraneStatus());
	}

	@Test
	public void testMultipleUpdates() {
		EcuRealtimeData ecuData = new EcuRealtimeData();

		int[] modeParentValues = {1, 3, 5, 7, 9};
		int[] modeChildValues = {2, 4, 6, 8, 10};
		int[] craneStatusValues = {0, 1, 2, 3, 4};

		for (int i = 0; i < modeParentValues.length; i++) {
			ecuData.setModeParent(modeParentValues[i]);
			ecuData.setModeChild(modeChildValues[i]);
			ecuData.setCraneStatus(craneStatusValues[i]);

			assertEquals("ModeParent should match after update " + i,
				modeParentValues[i], ecuData.getModeParent());
			assertEquals("ModeChild should match after update " + i,
				modeChildValues[i], ecuData.getModeChild());
			assertEquals("CraneStatus should match after update " + i,
				craneStatusValues[i], ecuData.getCraneStatus());
		}
	}

	@Test
	public void testObjectState() {
		int initialModeParent = 5;
		int initialModeChild = 3;
		int initialCraneStatus = 7;

		EcuRealtimeData ecuData = new EcuRealtimeData();
		ecuData.setModeParent(initialModeParent);
		ecuData.setModeChild(initialModeChild);
		ecuData.setCraneStatus(initialCraneStatus);

		// Verify initial state
		assertEquals("Initial modeParent should match", initialModeParent, ecuData.getModeParent());
		assertEquals("Initial modeChild should match", initialModeChild, ecuData.getModeChild());
		assertEquals("Initial craneStatus should match", initialCraneStatus, ecuData.getCraneStatus());

		// Change one property
		int newModeParent = 10;
		ecuData.setModeParent(newModeParent);

		// Verify only the changed property is updated
		assertEquals("ModeParent should be updated", newModeParent, ecuData.getModeParent());
		assertEquals("ModeChild should remain unchanged", initialModeChild, ecuData.getModeChild());
		assertEquals("CraneStatus should remain unchanged", initialCraneStatus, ecuData.getCraneStatus());

		// Change another property
		int newModeChild = 8;
		ecuData.setModeChild(newModeChild);

		// Verify the state
		assertEquals("ModeParent should remain as updated", newModeParent, ecuData.getModeParent());
		assertEquals("ModeChild should be updated", newModeChild, ecuData.getModeChild());
		assertEquals("CraneStatus should remain unchanged", initialCraneStatus, ecuData.getCraneStatus());
	}

	@Test
	public void testEcuRealtimeDataEquality() {
		EcuRealtimeData ecuData1 = new EcuRealtimeData();
		EcuRealtimeData ecuData2 = new EcuRealtimeData();
		EcuRealtimeData ecuData3 = new EcuRealtimeData();

		ecuData1.setModeParent(5);
		ecuData1.setModeChild(3);
		ecuData1.setCraneStatus(7);

		ecuData2.setModeParent(5);
		ecuData2.setModeChild(3);
		ecuData2.setCraneStatus(7);

		ecuData3.setModeParent(6);
		ecuData3.setModeChild(3);
		ecuData3.setCraneStatus(7);

		// Note: This test assumes EcuRealtimeData doesn't override equals()
		// Test that objects with same data have same field values
		assertEquals("Same modeParent values should match",
			ecuData1.getModeParent(), ecuData2.getModeParent());
		assertEquals("Same modeChild values should match",
			ecuData1.getModeChild(), ecuData2.getModeChild());
		assertEquals("Same craneStatus values should match",
			ecuData1.getCraneStatus(), ecuData2.getCraneStatus());

		// Test that objects with different data have different field values
		assertNotEquals("Different modeParent values should not match",
			ecuData1.getModeParent(), ecuData3.getModeParent());
	}

	@Test
	public void testToString() {
		EcuRealtimeData ecuData = new EcuRealtimeData();
		ecuData.setModeParent(5);
		ecuData.setModeChild(3);
		ecuData.setCraneStatus(7);

		String toStringResult = ecuData.toString();

		assertNotNull("toString() should not return null", toStringResult);
		assertTrue("toString() should not be empty", toStringResult.length() > 0);
	}

	@Test
	public void testRealWorldEcuScenario() {
		// Test real-world ECU scenarios for tower crane operations

		// Scenario 1: Crane in idle mode
		EcuRealtimeData idleData = new EcuRealtimeData();
		idleData.setModeParent(0);  // Idle parent mode
		idleData.setModeChild(0);   // Idle child mode
		idleData.setCraneStatus(0); // Idle status

		assertEquals("Idle crane should have mode parent 0", 0, idleData.getModeParent());
		assertEquals("Idle crane should have mode child 0", 0, idleData.getModeChild());
		assertEquals("Idle crane should have status 0", 0, idleData.getCraneStatus());

		// Scenario 2: Crane in lifting mode
		EcuRealtimeData liftingData = new EcuRealtimeData();
		liftingData.setModeParent(1);  // Lifting parent mode
		liftingData.setModeChild(2);   // Specific lifting operation
		liftingData.setCraneStatus(1); // Active status

		assertEquals("Lifting crane should have mode parent 1", 1, liftingData.getModeParent());
		assertEquals("Lifting crane should have mode child 2", 2, liftingData.getModeChild());
		assertEquals("Lifting crane should have status 1", 1, liftingData.getCraneStatus());

		// Scenario 3: Crane in error mode
		EcuRealtimeData errorData = new EcuRealtimeData();
		errorData.setModeParent(9);   // Error parent mode
		errorData.setModeChild(99);   // Specific error code
		errorData.setCraneStatus(9);  // Error status

		assertEquals("Error crane should have mode parent 9", 9, errorData.getModeParent());
		assertEquals("Error crane should have mode child 99", 99, errorData.getModeChild());
		assertEquals("Error crane should have status 9", 9, errorData.getCraneStatus());
	}

	@Test
	public void testEcuDataTransition() {
		// Test ECU data state transitions
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Start in idle state
		ecuData.setModeParent(0);
		ecuData.setModeChild(0);
		ecuData.setCraneStatus(0);

		// Transition to lifting state
		ecuData.setModeParent(1);
		ecuData.setModeChild(1);
		ecuData.setCraneStatus(1);

		assertEquals("Should transition to lifting mode parent", 1, ecuData.getModeParent());
		assertEquals("Should transition to lifting mode child", 1, ecuData.getModeChild());
		assertEquals("Should transition to lifting status", 1, ecuData.getCraneStatus());

		// Transition to moving state
		ecuData.setModeParent(2);
		ecuData.setModeChild(3);
		ecuData.setCraneStatus(2);

		assertEquals("Should transition to moving mode parent", 2, ecuData.getModeParent());
		assertEquals("Should transition to moving mode child", 3, ecuData.getModeChild());
		assertEquals("Should transition to moving status", 2, ecuData.getCraneStatus());

		// Transition back to idle
		ecuData.setModeParent(0);
		ecuData.setModeChild(0);
		ecuData.setCraneStatus(0);

		assertEquals("Should transition back to idle mode parent", 0, ecuData.getModeParent());
		assertEquals("Should transition back to idle mode child", 0, ecuData.getModeChild());
		assertEquals("Should transition back to idle status", 0, ecuData.getCraneStatus());
	}

	@Test
	public void testEcuRealtimeDataConsistency() {
		// Test that ECU data values remain consistent across operations
		EcuRealtimeData ecuData = new EcuRealtimeData();

		for (int i = 0; i < 100; i++) {
			int modeParent = i % 10;
			int modeChild = (i + 1) % 15;
			int craneStatus = (i + 2) % 5;

			ecuData.setModeParent(modeParent);
			ecuData.setModeChild(modeChild);
			ecuData.setCraneStatus(craneStatus);

			assertEquals("ModeParent should be consistent at iteration " + i,
				modeParent, ecuData.getModeParent());
			assertEquals("ModeChild should be consistent at iteration " + i,
				modeChild, ecuData.getModeChild());
			assertEquals("CraneStatus should be consistent at iteration " + i,
				craneStatus, ecuData.getCraneStatus());
		}
	}

	@Test
	public void testEcuDataValidation() {
		// Test ECU data validation scenarios
		EcuRealtimeData ecuData = new EcuRealtimeData();

		// Test valid operational ranges (assuming 0-15 for modes, 0-9 for status)
		for (int i = 0; i <= 15; i++) {
			ecuData.setModeParent(i);
			ecuData.setModeChild(i);
			assertEquals("Valid mode parent should be stored", i, ecuData.getModeParent());
			assertEquals("Valid mode child should be stored", i, ecuData.getModeChild());
		}

		for (int i = 0; i <= 9; i++) {
			ecuData.setCraneStatus(i);
			assertEquals("Valid crane status should be stored", i, ecuData.getCraneStatus());
		}
	}

	@Test
	public void testEcuDataCombinations() {
		// Test various combinations of ECU data values
		EcuRealtimeData ecuData = new EcuRealtimeData();

		int[][] testCombinations = {
			{0, 0, 0}, // Idle
			{1, 1, 1}, // Basic operation
			{2, 5, 2}, // Complex operation
			{5, 10, 3}, // Advanced operation
			{9, 15, 9}, // Error condition
			{15, 0, 5}, // Mixed values
		};

		for (int[] combination : testCombinations) {
			ecuData.setModeParent(combination[0]);
			ecuData.setModeChild(combination[1]);
			ecuData.setCraneStatus(combination[2]);

			assertEquals("Mode parent should match combination",
				combination[0], ecuData.getModeParent());
			assertEquals("Mode child should match combination",
				combination[1], ecuData.getModeChild());
			assertEquals("Crane status should match combination",
				combination[2], ecuData.getCraneStatus());
		}
	}

	@Test
	public void testEcuDataImmutabilityAfterRetrieval() {
		// Test that retrieved values don't affect the original object
		EcuRealtimeData ecuData = new EcuRealtimeData();
		ecuData.setModeParent(5);
		ecuData.setModeChild(3);
		ecuData.setCraneStatus(7);

		// Get the values
		int retrievedModeParent = ecuData.getModeParent();
		int retrievedModeChild = ecuData.getModeChild();
		int retrievedCraneStatus = ecuData.getCraneStatus();

		// Verify they match
		assertEquals("Retrieved mode parent should match", 5, retrievedModeParent);
		assertEquals("Retrieved mode child should match", 3, retrievedModeChild);
		assertEquals("Retrieved crane status should match", 7, retrievedCraneStatus);

		// Since int is primitive, modifying retrieved values shouldn't affect original
		retrievedModeParent = 10;
		retrievedModeChild = 8;
		retrievedCraneStatus = 2;

		// Original object should be unchanged
		assertEquals("Original mode parent should be unchanged", 5, ecuData.getModeParent());
		assertEquals("Original mode child should be unchanged", 3, ecuData.getModeChild());
		assertEquals("Original crane status should be unchanged", 7, ecuData.getCraneStatus());
	}
}
