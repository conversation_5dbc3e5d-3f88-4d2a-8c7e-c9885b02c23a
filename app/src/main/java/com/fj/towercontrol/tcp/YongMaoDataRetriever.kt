package com.fj.towercontrol.tcp

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.fj.fjprotocol.ProtocolHelper
import com.fj.fjprotocol.TransManager
import com.fj.towercontrol.data.entity.YongMaoRawData
import com.fj.towercontrol.util.FileStore
import com.fjd.app.common.tcp.client.TcpClient
import com.fjd.app.common.tcp.client.TcpClient.Callback
import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger
import java.util.Locale
import java.util.concurrent.Executors

/**
 * 永茂STT153平头塔吊485数据读取
 *
 * <AUTHOR>
 * @since 2024/11/21
 */
object YongMaoDataRetriever {

	private const val TAG = "YongMaoDataRetriever"

	private var tcpClient: TcpClient? = null

	private var handler = Handler(Looper.getMainLooper())
	private var parserExecutor = Executors.newSingleThreadExecutor()

	fun connect() {
		if (tcpClient == null) {
			tcpClient =
				TcpClient(FileStore.getSystemConfig().yongMao485Url.host, 51001, 2_000, object : Callback {
					override fun onConnectStatusChanged(connected: Boolean) {
						Logger.i(TAG, "onConnectStatusChanged: $connected")
						if (connected) {
							handler.removeCallbacksAndMessages(null)
							sendQueryCmd()
						} else {
							handler.postDelayed({ connect() }, 2_000)
						}
					}

					override fun onDataReceived(data: ByteArray) {
						Log.d(TAG, "onDataReceived: ${DataUtil.byte2hex(data)}")
						TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.sendYongMaoData(data))
						parseData(data)
					}

				})
		}
		tcpClient?.connect()
	}

	private fun sendQueryCmd() {
		//起始位
		var cmd = byteArrayOf('#'.code.toByte())
		//命令类型
		cmd += 0x02
		//参数地址
		cmd += byteArrayOf(0x00, 0x00)
		//参数数值
		cmd += byteArrayOf(0x00, 0x00, 0x00, 0x00)
		//crc校验
		cmd += DataUtil.yongMaoCrc16(cmd, cmd.size)
		Log.d(TAG, "sendQueryCmd: ${DataUtil.byte2hex(cmd)}")
		tcpClient?.send(cmd)

		handler.postDelayed({ sendQueryCmd() }, 200)
	}

	fun disconnect() {
		handler.removeCallbacksAndMessages(null)
		tcpClient?.disconnect()
		tcpClient = null
	}

	private fun parseData(data: ByteArray) {
		parserExecutor.execute {
			YongMaoRawData.parse(data).apply {
				Log.d(
					TAG, """
					回转角度:${rotateAngle.format2()}, 小车幅度:${trolleyAmplitude.format2()}, 动臂俯仰角:${jibPitchAngle},
					吊钩高度:${hookHeight}, 吊钩起重量:${hookWeight}, 起重力矩百分比:${liftingMomentPercent},
					风速:${windSpeed}, 大臂长度:${jibLength}, 转角传感器值:${angleSensor},
					高度传感器值:${heightSensor}, 幅度传感器值:${amplitudeSensor}, 重量传感器值:${weightSensor},
					转角标定系数:${angleCalibrationFactor}, 高度标定系数:${heightCalibrationFactor},
					幅度标定系数:${amplitudeCalibrationFactor}, 重量标定系数一:${weightCalibrationFactor1},
					重量标定系数二:${weightCalibrationFactor2}, 重量标定系数三:${weightCalibrationFactor3},
					吊钩倍率:${hookMultiple}, 状态位A:${statusA}
				""".trimIndent()
				)
			}
		}
	}

	private fun Float.format2(): String = String.format(Locale.getDefault(), "%.2f", this)
}
