package com.fj.towercontrol.tcp

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.fj.fjprotocol.ProtocolHelper
import com.fj.fjprotocol.TransManager
import com.fj.towercontrol.data.entity.YongMaoEcuData
import com.fj.towercontrol.data.entity.YongMaoRawData
import com.fj.towercontrol.util.FileStore
import com.fjd.app.common.tcp.client.TcpClient
import com.fjd.app.common.tcp.client.TcpClient.Callback
import com.fjd.app.common.util.DataUtil
import com.fjdynamics.app.logger.Logger
import java.util.concurrent.Executors

/**
 * 永茂STT153平头塔吊485数据读取
 *
 * <AUTHOR>
 * @since 2024/11/21
 */
object YongMaoDataRetriever {

	private const val TAG = "YongMaoDataRetriever"

	private var tcpClient: TcpClient? = null

	private var handler = Handler(Looper.getMainLooper())
	private var parserExecutor = Executors.newSingleThreadExecutor()

	fun connect() {
		if (tcpClient == null) {
			tcpClient =
				TcpClient(FileStore.getSystemConfig().yongMao485Url.host, 51001, 2_000, object : Callback {
					override fun onConnectStatusChanged(connected: Boolean) {
						Logger.i(TAG, "onConnectStatusChanged: $connected")
						if (connected) {
							handler.removeCallbacksAndMessages(null)
							sendQueryCmd()
						} else {
							handler.postDelayed({ connect() }, 2_000)
						}
					}

					override fun onDataReceived(data: ByteArray) {
						Log.d(TAG, "onDataReceived: ${DataUtil.byte2hex(data)}")
						TransManager.getInstance().sendEcuDataWithUart1(ProtocolHelper.sendYongMaoData(data))
						parseData(data)
					}

				})
		}
		tcpClient?.connect()
	}

	private fun sendQueryCmd() {
		//起始位
		var cmd = byteArrayOf('#'.code.toByte())
		//命令类型
		cmd += 0x02
		//参数地址
		cmd += byteArrayOf(0x00, 0x00)
		//参数数值
		cmd += byteArrayOf(0x00, 0x00, 0x00, 0x00)
		//crc校验
		cmd += DataUtil.yongMaoCrc16(cmd, cmd.size)
		Log.d(TAG, "sendQueryCmd: ${DataUtil.byte2hex(cmd)}")
		tcpClient?.send(cmd)

		handler.postDelayed({ sendQueryCmd() }, 200)
	}

	fun disconnect() {
		handler.removeCallbacksAndMessages(null)
		tcpClient?.disconnect()
		tcpClient = null
	}

	private fun parseData(data: ByteArray) {
		parserExecutor.execute {
			val yongMaoData = YongMaoRawData.parse(data)

		}
	}

}
