package com.fj.towercontrol.data.entity

import com.fjdynamics.app.logger.Logger
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 永茂STT153平头塔吊原始数据
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
data class YongMaoRawData(
	/**
	 * 回转角度
	 */
	var rotateAngle: Float = 0f,
	/**
	 * 小车幅度
	 */
	var trolleyAmplitude: Float = 0f,
	/**
	 * 动臂俯仰角
	 */
	var jibPitchAngle: Float = 0f,
	/**
	 * 吊钩高度
	 */
	var hookHeight: Float = 0f,
	/**
	 * 吊钩起重量
	 */
	var hookWeight: Float = 0f,
	/**
	 * 起重力矩百分比(xx%)
	 */
	var liftingMomentPercent: Float = 0f,
	/**
	 * 风速(m/s)
	 */
	var windSpeed: Float = 0f,
	/**
	 * 大臂长度(m)
	 */
	var jibLength: Float = 0f,
	/**
	 * 转角传感器值
	 */
	var angleSensor: Float = 0f,
	/**
	 * 高度传感器值
	 */
	var heightSensor: Float = 0f,
	/**
	 * 幅度传感器值
	 */
	var amplitudeSensor: Float = 0f,
	/**
	 * 重量传感器值
	 */
	var weightSensor: Float = 0f,
	/**
	 * 转角标定系数
	 */
	var angleCalibrationFactor: Float = 0f,
	/**
	 * 高度标定系数
	 */
	var heightCalibrationFactor: Float = 0f,
	/**
	 * 幅度标定系数
	 */
	var amplitudeCalibrationFactor: Float = 0f,
	/**
	 * 重量标定时第一段系数
	 */
	var weightCalibrationFactor1: Float = 0f,
	/**
	 * 重量标定时第二段系数
	 */
	var weightCalibrationFactor2: Float = 0f,
	/**
	 * 重量标定时第三段系数
	 */
	var weightCalibrationFactor3: Float = 0f,
	/**
	 * 吊钩倍率
	 */
	var hookMultiple: Float = 0f,
	/**
	 * 状态位A
	 */
	var statusA: Int = 0,
) {
	companion object {
		private const val TAG = "YongMaoRawData"

		fun parse(bytes: ByteArray): YongMaoRawData {
			val data = YongMaoRawData()

			if (bytes.size < 82) {
				Logger.e(TAG, "Data length insufficient: ${bytes.size}, expected: 82")
				return data
			}

			ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN).apply {
				try {
					position(4)
					data.rotateAngle = getInt() * 0.01f
					data.trolleyAmplitude = getInt() * 0.01f
					data.jibPitchAngle = getInt() * 0.01f
					data.hookHeight = getInt() * 0.01f
					data.hookWeight = getInt() * 0.01f
					data.liftingMomentPercent = getInt() * 0.01f
					data.windSpeed = getInt() * 0.01f
					data.jibLength = getInt() * 0.01f
					data.angleSensor = getInt() * 0.01f
					data.heightSensor = getInt() * 0.01f
					data.amplitudeSensor = getInt() * 0.01f
					data.weightSensor = getInt() * 0.01f
					data.angleCalibrationFactor = getInt() * 0.01f
					data.heightCalibrationFactor = getInt() * 0.01f
					data.amplitudeCalibrationFactor = getInt() * 0.01f
					data.weightCalibrationFactor1 = getInt() * 0.01f
					data.weightCalibrationFactor2 = getInt() * 0.01f
					data.weightCalibrationFactor3 = getInt() * 0.01f
					data.hookMultiple = getShort() * 0.01f
					data.statusA = getShort().toInt()
				} catch (e: Exception) {
					Logger.e(TAG, "Data parse exception: " + e.message)
				}
			}
			return data
		}
	}
}
