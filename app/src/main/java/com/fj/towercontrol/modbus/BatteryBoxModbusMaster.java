package com.fj.towercontrol.modbus;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.fj.towercontrol.data.entity.BatteryInfo;
import com.fj.towercontrol.event.MessageEvent;
import com.fj.towercontrol.util.CustomModbusReq;
import com.fjdynamics.app.logger.Logger;
import com.zgkxzx.modbus4And.requset.ModbusParam;
import com.zgkxzx.modbus4And.requset.OnRequestBack;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

/**
 * 平头塔吊电池箱Modbus通信
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public class BatteryBoxModbusMaster {
	private static final String TAG = "BatteryBoxModbusMaster";
	/**
	 * 尝试重连消息
	 */
	private static final int MSG_RETRY_CONNECT = 10001;
	/**
	 * 重置告警状态消息
	 */
	private static final int MSG_READ_NEXT_DATA = 10002;
	private static final int SLAVE_ID = 0xd2;
	private static final int START_ADDRESS = 0x28;
	private static final int READ_REGISTER_COUNT = 8;
	private static final long READ_REGISTER_INTERVAL = 10_000L;
	private final Handler handler;
	private final CustomModbusReq modbusReq;
	private BatteryInfo cacacheBatteryInfo;
	private int batteryErrorCount = 0;

	private BatteryBoxModbusMaster() {
		modbusReq = new CustomModbusReq();
		handler = new Handler(Looper.getMainLooper()) {
			@Override
			public void handleMessage(@NonNull Message msg) {
				if (msg.what == MSG_RETRY_CONNECT) {
					initModbus();
				} else if (msg.what == MSG_READ_NEXT_DATA) {
					readBatteryData();
				}
			}
		};
	}

	private static class SingletonHolder {
		private static final BatteryBoxModbusMaster INSTANCE = new BatteryBoxModbusMaster();
	}

	public static BatteryBoxModbusMaster getInstance() {
		return BatteryBoxModbusMaster.SingletonHolder.INSTANCE;
	}

	public void start() {
		handler.postDelayed(this::initModbus, 2_000);
	}

	public void stop() {
		modbusReq.destroy();
		handler.removeCallbacksAndMessages(null);
	}

	private void initModbus() {
		modbusReq.destroy();
		ModbusParam modbusParam = new ModbusParam()
			.setHost("************")
			.setPort(51001)
			.setEncapsulated(true)
			.setKeepAlive(true)
			.setTimeout(800)
			.setRetries(0);
		modbusReq.setParam(modbusParam)
			.init(new OnRequestBack<>() {
				@Override
				public void onSuccess(String s) {
					Log.d(TAG, "initModbus onSuccess: " + s);
					readBatteryData();
				}

				@Override
				public void onFailed(String msg) {
					Logger.e(TAG, "initModbus onFailed: " + msg);
					handler.sendEmptyMessageDelayed(MSG_RETRY_CONNECT, 2_000);
				}
			});
	}

	private void readBatteryData() {
		modbusReq.readHoldingRegisters(new OnRequestBack<>() {
			@Override
			public void onSuccess(short[] data) {
				// [125, 30000, 998, 4171, 4170, 62, 62, 0]
				Logger.d(TAG, "readBatteryData onSuccess: " + Arrays.toString(data));
				if (data == null || data.length < READ_REGISTER_COUNT) {
					return;
				}
				double percent = data[2] * 1.0 / 10; // 0.001,800/1000=80%
				int status = data[7]; // 0静止，1充电，2放电
				BatteryInfo batteryInfo = new BatteryInfo(percent, status);
				cacacheBatteryInfo = batteryInfo;
				EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_BOX_INFO_UPDATED, batteryInfo));
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, READ_REGISTER_INTERVAL);
			}

			@Override
			public void onFailed(String msg) {
				Logger.e(TAG, "readBatteryData onFailed: " + msg);
				batteryErrorCount++;
				if (batteryErrorCount < 3) {
					EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_BOX_INFO_UPDATED, cacacheBatteryInfo));
				} else {
					EventBus.getDefault().post(new MessageEvent(MessageEvent.BATTERY_BOX_INFO_UPDATED, null));
				}
				handler.sendEmptyMessageDelayed(MSG_READ_NEXT_DATA, 2_000);
			}
		}, SLAVE_ID, START_ADDRESS, READ_REGISTER_COUNT);
	}

}
