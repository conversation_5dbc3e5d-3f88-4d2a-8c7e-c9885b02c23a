package com.fj.towercontrol.util;

import com.fj.towercontrol.data.entity.BatteryInfo;
import com.fj.towercontrol.data.entity.HistoryPoint;
import com.fj.towercontrol.data.entity.LightningData;
import com.fj.towercontrol.data.entity.RopeDetectionData;
import com.fj.towercontrol.data.entity.SemiautomaticTask;
import com.fj.towercontrol.data.entity.SystemConfig;
import com.fj.towercontrol.data.entity.TowerConfig;
import com.fj.towercontrol.data.entity.TowerCraneData;
import com.fj.towercontrol.data.entity.WeatherStationData;
import com.fj.towercontrol.data.net.dto.platform.AlarmConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * 内存缓存
 *
 * <AUTHOR>
 */
public class MemoryStore {
	/**
	 * 采集模式：A->B
	 */
	public static final int COLLECT_MODE_A_TO_B = 0;
	/**
	 * 采集模式：B->A
	 */
	public static final int COLLECT_MODE_B_TO_A = 1;
	/**
	 * 采集状态：空闲
	 */
	public static final int COLLECT_STATUS_IDLE = 0;
	/**
	 * 采集状态：采集中
	 */
	public static final int COLLECT_STATUS_COLLECTING = 1;
	/**
	 * 轨迹点集合
	 */
	private final List<HistoryPoint> historyPointList = new LimitedArrayList<>(2_000);
	/**
	 * 当前任务id
	 */
	private String taskId;
	/**
	 * 任务采集 1：路径开始采集 2：卸料点采集 3：路径结束采集
	 */
	private int pathControlCommand;
	/**
	 * 读取到的吊钩电池信息
	 */
	private BatteryInfo batteryInfo;
	/**
	 * 平头小车电池箱电池信息
	 */
	private BatteryInfo batteryBoxInfo;
	/**
	 * 大屏锁屏状态<br>
	 * 0:锁屏<br>
	 * 1:未锁屏<br>
	 */
	private int loginStatus;
	/**
	 * 登录用户uid
	 */
	private Long uid;
	/**
	 * 从塔机读取到的数据
	 */
	private TowerCraneData towerCraneData;
	/**
	 * ecu上报的按键状态，存下来用于对比下一次数据更新时判断按键有没有发生变化
	 */
	private List<Integer> buttons = new ArrayList<>();
	/**
	 * 当前轨迹采集状态
	 * <p>
	 * 0:未开始<br/>
	 * 1:采集中<br/>
	 */
	private int pathCollectStatus;
	/**
	 * 当前轨迹采集模式
	 * <p>
	 * 0:A->B<br/>
	 * 1:B->A<br/>
	 */
	private int pathCollectMode;
	/**
	 * 塔上控制信号
	 */
	private Boolean cutoff;
	/**
	 * 风标指示灯信号
	 */
	private Boolean lightOn;
	/**
	 * 气象站数据
	 */
	private WeatherStationData weatherStationData;
	/**
	 * 当前吊钩高程与大地水准面差距
	 */
	private double hookGeoidalHeight;
	/**
	 * 发生333告警时是否限控
	 */
	private boolean limitControlBy333;

	private SystemConfig systemConfig;
	/**
	 * 平台配置的塔吊属性
	 */
	private TowerConfig towerConfig;
	/**
	 * 临时存储的半自动吊运任务，仅用于本地ecu调试
	 */
	private SemiautomaticTask semiautomaticTask;
	/**
	 * 雷电预警数据
	 */
	private LightningData lightningData;
	/**
	 * 缆绳检测数据
	 */
	private RopeDetectionData ropeDetectionData;
	/**
	 * 平台预警配置
	 */
	private List<AlarmConfig> alarmConfigList;

	private MemoryStore() {
		limitControlBy333 = FileStore.getLimitControlBy333();
		systemConfig = FileStore.getSystemConfig();
		towerConfig = FileStore.getTowerConfig();
		alarmConfigList = FileStore.getAlarmConfigList();
	}

	private static class SingletonHolder {
		private static final MemoryStore INSTANCE = new MemoryStore();
	}

	public static MemoryStore getInstance() {
		return MemoryStore.SingletonHolder.INSTANCE;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public int getPathControlCommand() {
		return pathControlCommand;
	}

	public void setPathControlCommand(int pathControlCommand) {
		this.pathControlCommand = pathControlCommand;
	}

	public BatteryInfo getBatteryInfo() {
		return batteryInfo;
	}

	public void setBatteryInfo(BatteryInfo batteryInfo) {
		this.batteryInfo = batteryInfo;
	}

	public BatteryInfo getBatteryBoxInfo() {
		return batteryBoxInfo;
	}

	public void setBatteryBoxInfo(BatteryInfo batteryBoxInfo) {
		this.batteryBoxInfo = batteryBoxInfo;
	}

	public int getLoginStatus() {
		return loginStatus;
	}

	public void setLoginStatus(int loginStatus) {
		this.loginStatus = loginStatus;
	}

	public TowerCraneData getTowerCraneData() {
		return towerCraneData;
	}

	public void setTowerCraneData(TowerCraneData towerCraneData) {
		this.towerCraneData = towerCraneData;
	}

	public List<Integer> getButtons() {
		return buttons;
	}

	public void setButtons(List<Integer> buttons) {
		this.buttons = buttons;
	}

	public int getPathCollectStatus() {
		return pathCollectStatus;
	}

	public void setPathCollectStatus(int pathCollectStatus) {
		this.pathCollectStatus = pathCollectStatus;
	}

	public int getPathCollectMode() {
		return pathCollectMode;
	}

	public void setPathCollectMode(int pathCollectMode) {
		this.pathCollectMode = pathCollectMode;
	}

	public List<HistoryPoint> getHistoryPointList() {
		return historyPointList;
	}

	public Boolean getCutoff() {
		return cutoff;
	}

	public void setCutoff(Boolean cutoff) {
		this.cutoff = cutoff;
	}

	public Boolean getLightOn() {
		return lightOn;
	}

	public void setLightOn(Boolean lightOn) {
		this.lightOn = lightOn;
	}

	public WeatherStationData getWeatherStationData() {
		return weatherStationData;
	}

	public void setWeatherStationData(WeatherStationData weatherStationData) {
		this.weatherStationData = weatherStationData;
	}

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public double getHookGeoidalHeight() {
		return hookGeoidalHeight;
	}

	public void setHookGeoidalHeight(double hookGeoidalHeight) {
		this.hookGeoidalHeight = hookGeoidalHeight;
	}

	public SystemConfig getSystemConfig() {
		return systemConfig;
	}

	public void setSystemConfig(SystemConfig systemConfig) {
		this.systemConfig = systemConfig;
	}

	public boolean isLimitControlBy333() {
		return limitControlBy333;
	}

	public void setLimitControlBy333(boolean limitControlBy333) {
		this.limitControlBy333 = limitControlBy333;
	}

	public TowerConfig getTowerConfig() {
		return towerConfig;
	}

	public void setTowerConfig(TowerConfig towerConfig) {
		this.towerConfig = towerConfig;
	}

	public SemiautomaticTask getSemiautomaticTask() {
		return semiautomaticTask;
	}

	public void setSemiautomaticTask(SemiautomaticTask semiautomaticTask) {
		this.semiautomaticTask = semiautomaticTask;
	}

	public LightningData getLightningData() {
		return lightningData;
	}

	public void setLightningData(LightningData lightningData) {
		this.lightningData = lightningData;
	}

	public RopeDetectionData getRopeDetectionData() {
		return ropeDetectionData;
	}

	public void setRopeDetectionData(RopeDetectionData ropeDetectionData) {
		this.ropeDetectionData = ropeDetectionData;
	}

	public List<AlarmConfig> getAlarmConfigList() {
		return alarmConfigList;
	}

	public void setAlarmConfigList(List<AlarmConfig> alarmConfigList) {
		this.alarmConfigList = alarmConfigList;
	}
}
