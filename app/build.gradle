import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
	alias(libs.plugins.android.application)
	alias(libs.plugins.kotlin.android)
	alias(libs.plugins.sentry)
}

android {
	namespace 'com.fj.towercontrol'
	compileSdk versions.compileSdk

	defaultConfig {
		applicationId "com.fj.towercontrol"
		minSdk versions.minSdk
		targetSdk versions.targetSdk
		versionCode 1
		versionName "1.1.7.2"

		testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

		ndk {
			//只支持AT2控制盒
			abiFilters 'armeabi-v7a'
		}
	}

	signingConfigs {
		config {
			storeFile file("../platform.jks")
			storePassword "fj171216"
			keyAlias "fj"
			keyPassword "fj171216"
		}
	}

	buildTypes {
		release {
			minifyEnabled false
			proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
			signingConfig signingConfigs.config
		}
		debug {
			minifyEnabled false
			proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
			signingConfig signingConfigs.config
		}
	}

	flavorDimensions = ["env"]
	productFlavors {
		qa {
			getIsDefault().set(true)
		}
		prod {}
	}

	applicationVariants.configureEach { variant ->
		variant.outputs.configureEach { output ->
			def versionName = variant.versionName
			def flavorName = variant.flavorName
			def buildType = variant.buildType.name
			def timestamp = new Date().format('yyyyMMddHHmmss')
			outputFileName = "TowerControl_${versionName}_${flavorName}_${buildType}_${timestamp}.apk"
		}
	}

	compileOptions {
		coreLibraryDesugaringEnabled true
		sourceCompatibility JavaVersion.VERSION_21
		targetCompatibility JavaVersion.VERSION_21
	}

	packagingOptions {
		jniLibs {
			pickFirsts += ['**/libc++_shared.so']
		}
		resources {
			excludes += ['META-INF/*']
		}
	}

	buildFeatures {
		viewBinding true
		buildConfig true
	}

	configurations {
		configureEach {
			exclude module: 'httpclient'
			exclude module: 'commons-logging'
			exclude group: "io.sentry", module: "sentry-android-timber"
		}
	}

	lint {
		abortOnError false
		checkReleaseBuilds false
	}
}

kotlin {
	compilerOptions {
		jvmTarget = JvmTarget.JVM_21
	}
}

dependencies {
	coreLibraryDesugaring libs.desugar.jdk
	implementation files('libs/modbus4Android-1.2.jar')
	implementation project(path: ':common')
	implementation project(path: ':fjprotocol')
	implementation project(path: ':logger')
	implementation libs.androidx.core.ktx
	implementation libs.androidx.appcompat
	implementation libs.androidx.activity
	implementation libs.androidx.activity.ktx
	implementation libs.androidx.fragment
	implementation libs.androidx.fragment.ktx
	implementation libs.google.material
	implementation libs.androidx.constraintlayout
	implementation libs.eventbus
	implementation libs.mmkv
	implementation libs.hivemq
	implementation libs.bcpkix.jdk15on
	implementation libs.gson
	implementation libs.opencsv
	implementation libs.utilcode
	implementation libs.bundles.retrofit
	implementation platform(libs.okhttp.bom)
	implementation libs.bundles.okhttp
	implementation libs.joda.time
	implementation libs.fjdynamics.math
//	implementation libs.bundles.umeng
	implementation libs.marineapi

	testImplementation libs.bundles.test.unit

	androidTestImplementation libs.bundles.test.android
}
